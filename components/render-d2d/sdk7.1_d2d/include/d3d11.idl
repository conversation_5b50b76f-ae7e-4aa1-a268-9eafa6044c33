////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// D3D11 IDL
//
// Contains interface definitions for the D3D11 API.
//
// Copyright (C) Microsoft Corporation
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

import "oaidl.idl";
import "ocidl.idl";
import "dxgi.idl";
import "d3dcommon.idl";

// NOTE: The following constants are generated from the D3D11 hardware spec.  Do not edit these values directly.
cpp_quote( "#ifndef _D3D11_CONSTANTS" )
cpp_quote( "#define _D3D11_CONSTANTS" )
const UINT D3D11_16BIT_INDEX_STRIP_CUT_VALUE = 0xffff;
const UINT D3D11_32BIT_INDEX_STRIP_CUT_VALUE = 0xffffffff;
const UINT D3D11_8BIT_INDEX_STRIP_CUT_VALUE = 0xff;
const UINT D3D11_ARRAY_AXIS_ADDRESS_RANGE_BIT_COUNT = 9;
const UINT D3D11_CLIP_OR_CULL_DISTANCE_COUNT = 8;
const UINT D3D11_CLIP_OR_CULL_DISTANCE_ELEMENT_COUNT = 2;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT = 14;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_COMPONENTS = 4;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_HW_SLOT_COUNT = 15;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COUNT = 15;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D11_COMMONSHADER_FLOWCONTROL_NESTING_LIMIT = 64;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COUNT = 1;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D11_COMMONSHADER_IMMEDIATE_VALUE_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_COMPONENTS = 1;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_COUNT = 128;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_REGISTER_READ_PORTS = 1;
const UINT D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT = 128;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_COMPONENTS = 1;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_COUNT = 16;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_READS_PER_INST = 1;
const UINT D3D11_COMMONSHADER_SAMPLER_REGISTER_READ_PORTS = 1;
const UINT D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT = 16;
const UINT D3D11_COMMONSHADER_SUBROUTINE_NESTING_LIMIT = 32;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_COMPONENTS = 4;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_COUNT = 4096;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_READS_PER_INST = 3;
const UINT D3D11_COMMONSHADER_TEMP_REGISTER_READ_PORTS = 3;
const UINT D3D11_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MAX = 10;
const INT D3D11_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MIN = -10;
const INT D3D11_COMMONSHADER_TEXEL_OFFSET_MAX_NEGATIVE = -8;
const UINT D3D11_COMMONSHADER_TEXEL_OFFSET_MAX_POSITIVE = 7;
const UINT D3D11_CS_4_X_BUCKET00_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 256;
const UINT D3D11_CS_4_X_BUCKET00_MAX_NUM_THREADS_PER_GROUP = 64;
const UINT D3D11_CS_4_X_BUCKET01_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 240;
const UINT D3D11_CS_4_X_BUCKET01_MAX_NUM_THREADS_PER_GROUP = 68;
const UINT D3D11_CS_4_X_BUCKET02_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 224;
const UINT D3D11_CS_4_X_BUCKET02_MAX_NUM_THREADS_PER_GROUP = 72;
const UINT D3D11_CS_4_X_BUCKET03_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 208;
const UINT D3D11_CS_4_X_BUCKET03_MAX_NUM_THREADS_PER_GROUP = 76;
const UINT D3D11_CS_4_X_BUCKET04_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 192;
const UINT D3D11_CS_4_X_BUCKET04_MAX_NUM_THREADS_PER_GROUP = 84;
const UINT D3D11_CS_4_X_BUCKET05_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 176;
const UINT D3D11_CS_4_X_BUCKET05_MAX_NUM_THREADS_PER_GROUP = 92;
const UINT D3D11_CS_4_X_BUCKET06_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 160;
const UINT D3D11_CS_4_X_BUCKET06_MAX_NUM_THREADS_PER_GROUP = 100;
const UINT D3D11_CS_4_X_BUCKET07_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 144;
const UINT D3D11_CS_4_X_BUCKET07_MAX_NUM_THREADS_PER_GROUP = 112;
const UINT D3D11_CS_4_X_BUCKET08_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 128;
const UINT D3D11_CS_4_X_BUCKET08_MAX_NUM_THREADS_PER_GROUP = 128;
const UINT D3D11_CS_4_X_BUCKET09_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 112;
const UINT D3D11_CS_4_X_BUCKET09_MAX_NUM_THREADS_PER_GROUP = 144;
const UINT D3D11_CS_4_X_BUCKET10_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 96;
const UINT D3D11_CS_4_X_BUCKET10_MAX_NUM_THREADS_PER_GROUP = 168;
const UINT D3D11_CS_4_X_BUCKET11_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 80;
const UINT D3D11_CS_4_X_BUCKET11_MAX_NUM_THREADS_PER_GROUP = 204;
const UINT D3D11_CS_4_X_BUCKET12_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 64;
const UINT D3D11_CS_4_X_BUCKET12_MAX_NUM_THREADS_PER_GROUP = 256;
const UINT D3D11_CS_4_X_BUCKET13_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 48;
const UINT D3D11_CS_4_X_BUCKET13_MAX_NUM_THREADS_PER_GROUP = 340;
const UINT D3D11_CS_4_X_BUCKET14_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 32;
const UINT D3D11_CS_4_X_BUCKET14_MAX_NUM_THREADS_PER_GROUP = 512;
const UINT D3D11_CS_4_X_BUCKET15_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 16;
const UINT D3D11_CS_4_X_BUCKET15_MAX_NUM_THREADS_PER_GROUP = 768;
const UINT D3D11_CS_4_X_DISPATCH_MAX_THREAD_GROUPS_IN_Z_DIMENSION = 1;
const UINT D3D11_CS_4_X_RAW_UAV_BYTE_ALIGNMENT = 256;
const UINT D3D11_CS_4_X_THREAD_GROUP_MAX_THREADS_PER_GROUP = 768;
const UINT D3D11_CS_4_X_THREAD_GROUP_MAX_X = 768;
const UINT D3D11_CS_4_X_THREAD_GROUP_MAX_Y = 768;
const UINT D3D11_CS_4_X_UAV_REGISTER_COUNT = 1;
const UINT D3D11_CS_DISPATCH_MAX_THREAD_GROUPS_PER_DIMENSION = 65535;
const UINT D3D11_CS_TGSM_REGISTER_COUNT = 8192;
const UINT D3D11_CS_TGSM_REGISTER_READS_PER_INST = 1;
const UINT D3D11_CS_TGSM_RESOURCE_REGISTER_COMPONENTS = 1;
const UINT D3D11_CS_TGSM_RESOURCE_REGISTER_READ_PORTS = 1;
const UINT D3D11_CS_THREAD_GROUP_MAX_THREADS_PER_GROUP = 1024;
const UINT D3D11_CS_THREAD_GROUP_MAX_X = 1024;
const UINT D3D11_CS_THREAD_GROUP_MAX_Y = 1024;
const UINT D3D11_CS_THREAD_GROUP_MAX_Z = 64;
const UINT D3D11_CS_THREAD_GROUP_MIN_X = 1;
const UINT D3D11_CS_THREAD_GROUP_MIN_Y = 1;
const UINT D3D11_CS_THREAD_GROUP_MIN_Z = 1;
const UINT D3D11_CS_THREAD_LOCAL_TEMP_REGISTER_POOL = 16384;
cpp_quote( "#define D3D11_DEFAULT_BLEND_FACTOR_ALPHA	( 1.0f )" )
cpp_quote( "#define D3D11_DEFAULT_BLEND_FACTOR_BLUE	( 1.0f )" )
cpp_quote( "#define D3D11_DEFAULT_BLEND_FACTOR_GREEN	( 1.0f )" )
cpp_quote( "#define D3D11_DEFAULT_BLEND_FACTOR_RED	( 1.0f )" )
cpp_quote( "#define D3D11_DEFAULT_BORDER_COLOR_COMPONENT	( 0.0f )" )
const UINT D3D11_DEFAULT_DEPTH_BIAS = 0;
cpp_quote( "#define D3D11_DEFAULT_DEPTH_BIAS_CLAMP	( 0.0f )" )
cpp_quote( "#define D3D11_DEFAULT_MAX_ANISOTROPY	( 16.0f )" )
cpp_quote( "#define D3D11_DEFAULT_MIP_LOD_BIAS	( 0.0f )" )
const UINT D3D11_DEFAULT_RENDER_TARGET_ARRAY_INDEX = 0;
const UINT D3D11_DEFAULT_SAMPLE_MASK = 0xffffffff;
const UINT D3D11_DEFAULT_SCISSOR_ENDX = 0;
const UINT D3D11_DEFAULT_SCISSOR_ENDY = 0;
const UINT D3D11_DEFAULT_SCISSOR_STARTX = 0;
const UINT D3D11_DEFAULT_SCISSOR_STARTY = 0;
cpp_quote( "#define D3D11_DEFAULT_SLOPE_SCALED_DEPTH_BIAS	( 0.0f )" )
const UINT D3D11_DEFAULT_STENCIL_READ_MASK = 0xff;
const UINT D3D11_DEFAULT_STENCIL_REFERENCE = 0;
const UINT D3D11_DEFAULT_STENCIL_WRITE_MASK = 0xff;
const UINT D3D11_DEFAULT_VIEWPORT_AND_SCISSORRECT_INDEX = 0;
const UINT D3D11_DEFAULT_VIEWPORT_HEIGHT = 0;
cpp_quote( "#define D3D11_DEFAULT_VIEWPORT_MAX_DEPTH	( 0.0f )" )
cpp_quote( "#define D3D11_DEFAULT_VIEWPORT_MIN_DEPTH	( 0.0f )" )
const UINT D3D11_DEFAULT_VIEWPORT_TOPLEFTX = 0;
const UINT D3D11_DEFAULT_VIEWPORT_TOPLEFTY = 0;
const UINT D3D11_DEFAULT_VIEWPORT_WIDTH = 0;
const UINT D3D11_DS_INPUT_CONTROL_POINTS_MAX_TOTAL_SCALARS = 3968;
const UINT D3D11_DS_INPUT_CONTROL_POINT_REGISTER_COMPONENTS = 4;
const UINT D3D11_DS_INPUT_CONTROL_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_DS_INPUT_CONTROL_POINT_REGISTER_COUNT = 32;
const UINT D3D11_DS_INPUT_CONTROL_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_DS_INPUT_CONTROL_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D11_DS_INPUT_DOMAIN_POINT_REGISTER_COMPONENTS = 3;
const UINT D3D11_DS_INPUT_DOMAIN_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_DS_INPUT_DOMAIN_POINT_REGISTER_COUNT = 1;
const UINT D3D11_DS_INPUT_DOMAIN_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_DS_INPUT_DOMAIN_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D11_DS_INPUT_PATCH_CONSTANT_REGISTER_COMPONENTS = 4;
const UINT D3D11_DS_INPUT_PATCH_CONSTANT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_DS_INPUT_PATCH_CONSTANT_REGISTER_COUNT = 32;
const UINT D3D11_DS_INPUT_PATCH_CONSTANT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_DS_INPUT_PATCH_CONSTANT_REGISTER_READ_PORTS = 1;
const UINT D3D11_DS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_DS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_DS_OUTPUT_REGISTER_COUNT = 32;
cpp_quote( "#define D3D11_FLOAT16_FUSED_TOLERANCE_IN_ULP	( 0.6 )" )
cpp_quote( "#define D3D11_FLOAT32_MAX	( 3.402823466e+38f )" )
cpp_quote( "#define D3D11_FLOAT32_TO_INTEGER_TOLERANCE_IN_ULP	( 0.6f )" )
cpp_quote( "#define D3D11_FLOAT_TO_SRGB_EXPONENT_DENOMINATOR	( 2.4f )" )
cpp_quote( "#define D3D11_FLOAT_TO_SRGB_EXPONENT_NUMERATOR	( 1.0f )" )
cpp_quote( "#define D3D11_FLOAT_TO_SRGB_OFFSET	( 0.055f )" )
cpp_quote( "#define D3D11_FLOAT_TO_SRGB_SCALE_1	( 12.92f )" )
cpp_quote( "#define D3D11_FLOAT_TO_SRGB_SCALE_2	( 1.055f )" )
cpp_quote( "#define D3D11_FLOAT_TO_SRGB_THRESHOLD	( 0.0031308f )" )
cpp_quote( "#define D3D11_FTOI_INSTRUCTION_MAX_INPUT	( 2147483647.999f )" )
cpp_quote( "#define D3D11_FTOI_INSTRUCTION_MIN_INPUT	( -2147483648.999f )" )
cpp_quote( "#define D3D11_FTOU_INSTRUCTION_MAX_INPUT	( 4294967295.999f )" )
cpp_quote( "#define D3D11_FTOU_INSTRUCTION_MIN_INPUT	( 0.0f )" )
const UINT D3D11_GS_INPUT_INSTANCE_ID_READS_PER_INST = 2;
const UINT D3D11_GS_INPUT_INSTANCE_ID_READ_PORTS = 1;
const UINT D3D11_GS_INPUT_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D11_GS_INPUT_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_GS_INPUT_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D11_GS_INPUT_PRIM_CONST_REGISTER_COMPONENTS = 1;
const UINT D3D11_GS_INPUT_PRIM_CONST_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_GS_INPUT_PRIM_CONST_REGISTER_COUNT = 1;
const UINT D3D11_GS_INPUT_PRIM_CONST_REGISTER_READS_PER_INST = 2;
const UINT D3D11_GS_INPUT_PRIM_CONST_REGISTER_READ_PORTS = 1;
const UINT D3D11_GS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_GS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_GS_INPUT_REGISTER_COUNT = 32;
const UINT D3D11_GS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_GS_INPUT_REGISTER_READ_PORTS = 1;
const UINT D3D11_GS_INPUT_REGISTER_VERTICES = 32;
const UINT D3D11_GS_MAX_INSTANCE_COUNT = 32;
const UINT D3D11_GS_MAX_OUTPUT_VERTEX_COUNT_ACROSS_INSTANCES = 1024;
const UINT D3D11_GS_OUTPUT_ELEMENTS = 32;
const UINT D3D11_GS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_GS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_GS_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D11_HS_CONTROL_POINT_PHASE_INPUT_REGISTER_COUNT = 32;
const UINT D3D11_HS_CONTROL_POINT_PHASE_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D11_HS_CONTROL_POINT_REGISTER_COMPONENTS = 4;
const UINT D3D11_HS_CONTROL_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_HS_CONTROL_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_HS_CONTROL_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D11_HS_FORK_PHASE_INSTANCE_COUNT_UPPER_BOUND = 0xFFFFFFFF;
const UINT D3D11_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D11_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D11_HS_INPUT_FORK_INSTANCE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D11_HS_INPUT_FORK_INSTANCE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D11_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D11_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D11_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D11_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D11_HS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D11_HS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_HS_INPUT_PRIMITIVE_ID_REGISTER_COUNT = 1;
const UINT D3D11_HS_INPUT_PRIMITIVE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D11_HS_INPUT_PRIMITIVE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D11_HS_JOIN_PHASE_INSTANCE_COUNT_UPPER_BOUND = 0xFFFFFFFF;
cpp_quote( "#define D3D11_HS_MAXTESSFACTOR_LOWER_BOUND	( 1.0f )" )
cpp_quote( "#define D3D11_HS_MAXTESSFACTOR_UPPER_BOUND	( 64.0f )" )
const UINT D3D11_HS_OUTPUT_CONTROL_POINTS_MAX_TOTAL_SCALARS = 3968;
const UINT D3D11_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COMPONENTS = 1;
const UINT D3D11_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COUNT = 1;
const UINT D3D11_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D11_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_READ_PORTS = 1;
const UINT D3D11_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COMPONENTS = 4;
const UINT D3D11_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COUNT = 32;
const UINT D3D11_HS_OUTPUT_PATCH_CONSTANT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_HS_OUTPUT_PATCH_CONSTANT_REGISTER_READ_PORTS = 1;
const UINT D3D11_IA_DEFAULT_INDEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D11_IA_DEFAULT_PRIMITIVE_TOPOLOGY = 0;
const UINT D3D11_IA_DEFAULT_VERTEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D11_IA_INDEX_INPUT_RESOURCE_SLOT_COUNT = 1;
const UINT D3D11_IA_INSTANCE_ID_BIT_COUNT = 32;
const UINT D3D11_IA_INTEGER_ARITHMETIC_BIT_COUNT = 32;
const UINT D3D11_IA_PATCH_MAX_CONTROL_POINT_COUNT = 32;
const UINT D3D11_IA_PRIMITIVE_ID_BIT_COUNT = 32;
const UINT D3D11_IA_VERTEX_ID_BIT_COUNT = 32;
const UINT D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT = 32;
const UINT D3D11_IA_VERTEX_INPUT_STRUCTURE_ELEMENTS_COMPONENTS = 128;
const UINT D3D11_IA_VERTEX_INPUT_STRUCTURE_ELEMENT_COUNT = 32;
const UINT D3D11_INTEGER_DIVIDE_BY_ZERO_QUOTIENT = 0xffffffff;
const UINT D3D11_INTEGER_DIVIDE_BY_ZERO_REMAINDER = 0xffffffff;
const UINT D3D11_KEEP_RENDER_TARGETS_AND_DEPTH_STENCIL = 0xffffffff;
const UINT D3D11_KEEP_UNORDERED_ACCESS_VIEWS = 0xffffffff;
cpp_quote( "#define D3D11_LINEAR_GAMMA	( 1.0f )" )
const UINT D3D11_MAJOR_VERSION = 11;
cpp_quote( "#define D3D11_MAX_BORDER_COLOR_COMPONENT	( 1.0f )" )
cpp_quote( "#define D3D11_MAX_DEPTH	( 1.0f )" )
const UINT D3D11_MAX_MAXANISOTROPY = 16;
const UINT D3D11_MAX_MULTISAMPLE_SAMPLE_COUNT = 32;
cpp_quote( "#define D3D11_MAX_POSITION_VALUE	( 3.402823466e+34f )" )
const UINT D3D11_MAX_TEXTURE_DIMENSION_2_TO_EXP = 17;
const UINT D3D11_MINOR_VERSION = 0;
cpp_quote( "#define D3D11_MIN_BORDER_COLOR_COMPONENT	( 0.0f )" )
cpp_quote( "#define D3D11_MIN_DEPTH	( 0.0f )" )
const UINT D3D11_MIN_MAXANISOTROPY = 0;
cpp_quote( "#define D3D11_MIP_LOD_BIAS_MAX	( 15.99f )" )
cpp_quote( "#define D3D11_MIP_LOD_BIAS_MIN	( -16.0f )" )
const UINT D3D11_MIP_LOD_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D11_MIP_LOD_RANGE_BIT_COUNT = 8;
cpp_quote( "#define D3D11_MULTISAMPLE_ANTIALIAS_LINE_WIDTH	( 1.4f )" )
const UINT D3D11_NONSAMPLE_FETCH_OUT_OF_RANGE_ACCESS_RESULT = 0;
const UINT D3D11_PIXEL_ADDRESS_RANGE_BIT_COUNT = 15;
const UINT D3D11_PRE_SCISSOR_PIXEL_ADDRESS_RANGE_BIT_COUNT = 16;
const UINT D3D11_PS_CS_UAV_REGISTER_COMPONENTS = 1;
const UINT D3D11_PS_CS_UAV_REGISTER_COUNT = 8;
const UINT D3D11_PS_CS_UAV_REGISTER_READS_PER_INST = 1;
const UINT D3D11_PS_CS_UAV_REGISTER_READ_PORTS = 1;
const UINT D3D11_PS_FRONTFACING_DEFAULT_VALUE = 0xFFFFFFFF;
const UINT D3D11_PS_FRONTFACING_FALSE_VALUE = 0x00000000;
const UINT D3D11_PS_FRONTFACING_TRUE_VALUE = 0xFFFFFFFF;
const UINT D3D11_PS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_PS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_INPUT_REGISTER_COUNT = 32;
const UINT D3D11_PS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_PS_INPUT_REGISTER_READ_PORTS = 1;
cpp_quote( "#define D3D11_PS_LEGACY_PIXEL_CENTER_FRACTIONAL_COMPONENT	( 0.0f )" )
const UINT D3D11_PS_OUTPUT_DEPTH_REGISTER_COMPONENTS = 1;
const UINT D3D11_PS_OUTPUT_DEPTH_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_OUTPUT_DEPTH_REGISTER_COUNT = 1;
const UINT D3D11_PS_OUTPUT_MASK_REGISTER_COMPONENTS = 1;
const UINT D3D11_PS_OUTPUT_MASK_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_OUTPUT_MASK_REGISTER_COUNT = 1;
const UINT D3D11_PS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_PS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_PS_OUTPUT_REGISTER_COUNT = 8;
cpp_quote( "#define D3D11_PS_PIXEL_CENTER_FRACTIONAL_COMPONENT	( 0.5f )" )
const UINT D3D11_RAW_UAV_SRV_BYTE_ALIGNMENT = 16;
const UINT D3D11_REQ_BLEND_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D11_REQ_BUFFER_RESOURCE_TEXEL_COUNT_2_TO_EXP = 27;
const UINT D3D11_REQ_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D11_REQ_DEPTH_STENCIL_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D11_REQ_DRAWINDEXED_INDEX_COUNT_2_TO_EXP = 32;
const UINT D3D11_REQ_DRAW_VERTEX_COUNT_2_TO_EXP = 32;
const UINT D3D11_REQ_FILTERING_HW_ADDRESSABLE_RESOURCE_DIMENSION = 16384;
const UINT D3D11_REQ_GS_INVOCATION_32BIT_OUTPUT_COMPONENT_LIMIT = 1024;
const UINT D3D11_REQ_IMMEDIATE_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D11_REQ_MAXANISOTROPY = 16;
const UINT D3D11_REQ_MIP_LEVELS = 15;
const UINT D3D11_REQ_MULTI_ELEMENT_STRUCTURE_SIZE_IN_BYTES = 2048;
const UINT D3D11_REQ_RASTERIZER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D11_REQ_RENDER_TO_BUFFER_WINDOW_WIDTH = 16384;
const UINT D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_A_TERM = 128;
cpp_quote( "#define D3D11_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_B_TERM	( 0.25f )" )
const UINT D3D11_REQ_RESOURCE_VIEW_COUNT_PER_DEVICE_2_TO_EXP = 20;
const UINT D3D11_REQ_SAMPLER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D11_REQ_TEXTURE1D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D11_REQ_TEXTURE1D_U_DIMENSION = 16384;
const UINT D3D11_REQ_TEXTURE2D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D11_REQ_TEXTURE2D_U_OR_V_DIMENSION = 16384;
const UINT D3D11_REQ_TEXTURE3D_U_V_OR_W_DIMENSION = 2048;
const UINT D3D11_REQ_TEXTURECUBE_DIMENSION = 16384;
const UINT D3D11_RESINFO_INSTRUCTION_MISSING_COMPONENT_RETVAL = 0;
const UINT D3D11_SHADER_MAJOR_VERSION = 5;
const UINT D3D11_SHADER_MAX_INSTANCES = 65535;
const UINT D3D11_SHADER_MAX_INTERFACES = 256;
const UINT D3D11_SHADER_MAX_INTERFACE_CALL_SITES = 4096;
const UINT D3D11_SHADER_MAX_TYPES = 65535;
const UINT D3D11_SHADER_MINOR_VERSION = 0;
const UINT D3D11_SHIFT_INSTRUCTION_PAD_VALUE = 0;
const UINT D3D11_SHIFT_INSTRUCTION_SHIFT_VALUE_BIT_COUNT = 5;
const UINT D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT = 8;
const UINT D3D11_SO_BUFFER_MAX_STRIDE_IN_BYTES = 2048;
const UINT D3D11_SO_BUFFER_MAX_WRITE_WINDOW_IN_BYTES = 512;
const UINT D3D11_SO_BUFFER_SLOT_COUNT = 4;
const UINT D3D11_SO_DDI_REGISTER_INDEX_DENOTING_GAP = 0xffffffff;
const UINT D3D11_SO_NO_RASTERIZED_STREAM = 0xffffffff;
const UINT D3D11_SO_OUTPUT_COMPONENT_COUNT = 128;
const UINT D3D11_SO_STREAM_COUNT = 4;
const UINT D3D11_SPEC_DATE_DAY = 19;
const UINT D3D11_SPEC_DATE_MONTH = 02;
const UINT D3D11_SPEC_DATE_YEAR = 2009;
cpp_quote( "#define D3D11_SPEC_VERSION	( 0.01 )" )
cpp_quote( "#define D3D11_SRGB_GAMMA	( 2.2f )" )
cpp_quote( "#define D3D11_SRGB_TO_FLOAT_DENOMINATOR_1	( 12.92f )" )
cpp_quote( "#define D3D11_SRGB_TO_FLOAT_DENOMINATOR_2	( 1.055f )" )
cpp_quote( "#define D3D11_SRGB_TO_FLOAT_EXPONENT	( 2.4f )" )
cpp_quote( "#define D3D11_SRGB_TO_FLOAT_OFFSET	( 0.055f )" )
cpp_quote( "#define D3D11_SRGB_TO_FLOAT_THRESHOLD	( 0.04045f )" )
cpp_quote( "#define D3D11_SRGB_TO_FLOAT_TOLERANCE_IN_ULP	( 0.5f )" )
const UINT D3D11_STANDARD_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_STANDARD_COMPONENT_BIT_COUNT_DOUBLED = 64;
const UINT D3D11_STANDARD_MAXIMUM_ELEMENT_ALIGNMENT_BYTE_MULTIPLE = 4;
const UINT D3D11_STANDARD_PIXEL_COMPONENT_COUNT = 128;
const UINT D3D11_STANDARD_PIXEL_ELEMENT_COUNT = 32;
const UINT D3D11_STANDARD_VECTOR_SIZE = 4;
const UINT D3D11_STANDARD_VERTEX_ELEMENT_COUNT = 32;
const UINT D3D11_STANDARD_VERTEX_TOTAL_COMPONENT_COUNT = 64;
const UINT D3D11_SUBPIXEL_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D11_SUBTEXEL_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D11_TESSELLATOR_MAX_EVEN_TESSELLATION_FACTOR = 64;
const UINT D3D11_TESSELLATOR_MAX_ISOLINE_DENSITY_TESSELLATION_FACTOR = 64;
const UINT D3D11_TESSELLATOR_MAX_ODD_TESSELLATION_FACTOR = 63;
const UINT D3D11_TESSELLATOR_MAX_TESSELLATION_FACTOR = 64;
const UINT D3D11_TESSELLATOR_MIN_EVEN_TESSELLATION_FACTOR = 2;
const UINT D3D11_TESSELLATOR_MIN_ISOLINE_DENSITY_TESSELLATION_FACTOR = 1;
const UINT D3D11_TESSELLATOR_MIN_ODD_TESSELLATION_FACTOR = 1;
const UINT D3D11_TEXEL_ADDRESS_RANGE_BIT_COUNT = 16;
const UINT D3D11_UNBOUND_MEMORY_ACCESS_RESULT = 0;
const UINT D3D11_VIEWPORT_AND_SCISSORRECT_MAX_INDEX = 15;
const UINT D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE = 16;
const UINT D3D11_VIEWPORT_BOUNDS_MAX = 32767;
const INT D3D11_VIEWPORT_BOUNDS_MIN = -32768;
const UINT D3D11_VS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_VS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_VS_INPUT_REGISTER_COUNT = 32;
const UINT D3D11_VS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D11_VS_INPUT_REGISTER_READ_PORTS = 1;
const UINT D3D11_VS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D11_VS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D11_VS_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D11_WHQL_CONTEXT_COUNT_FOR_RESOURCE_LIMIT = 10;
const UINT D3D11_WHQL_DRAWINDEXED_INDEX_COUNT_2_TO_EXP = 25;
const UINT D3D11_WHQL_DRAW_VERTEX_COUNT_2_TO_EXP = 25;
cpp_quote( "#endif" )

const UINT _FACD3D11 = 0x87c;
const UINT _FACD3D11DEBUG = _FACD3D11 + 1;

cpp_quote( "#define MAKE_D3D11_HRESULT( code )  MAKE_HRESULT( 1, _FACD3D11, code )" )
cpp_quote( "#define MAKE_D3D11_STATUS( code )    MAKE_HRESULT( 0, _FACD3D11, code )" )
cpp_quote( "#define D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS  MAKE_D3D11_HRESULT(1)" )
cpp_quote( "#define D3D11_ERROR_FILE_NOT_FOUND  MAKE_D3D11_HRESULT(2)" )
cpp_quote( "#define D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS  MAKE_D3D11_HRESULT(3)" )
cpp_quote( "#define D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD  MAKE_D3D11_HRESULT(4)" )

cpp_quote( "#if __SAL_H_FULL_VER < 140050727" )
cpp_quote( "#undef __in_range" )
cpp_quote( "#undef __in_xcount_opt" )
cpp_quote( "#define __in_range(x, y)" )
cpp_quote( "#define __in_xcount_opt(x)" )
cpp_quote( "#endif" )

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_DEFAULT {};" )
cpp_quote( "extern const DECLSPEC_SELECTANY CD3D11_DEFAULT D3D11_DEFAULT;" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

typedef enum D3D11_INPUT_CLASSIFICATION
{
    D3D11_INPUT_PER_VERTEX_DATA = 0,
    D3D11_INPUT_PER_INSTANCE_DATA = 1
} D3D11_INPUT_CLASSIFICATION;

const UINT D3D11_APPEND_ALIGNED_ELEMENT = 0xffffffff;

typedef struct D3D11_INPUT_ELEMENT_DESC
{
    LPCSTR SemanticName;
    UINT SemanticIndex;
    DXGI_FORMAT Format;
    UINT InputSlot;
    UINT AlignedByteOffset;
    D3D11_INPUT_CLASSIFICATION InputSlotClass;
    UINT InstanceDataStepRate;
} D3D11_INPUT_ELEMENT_DESC;

// Keep FILL_MODE values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_FILL_MODE
{
    // 1 was POINT in D3D, unused in D3D11
    D3D11_FILL_WIREFRAME = 2,
    D3D11_FILL_SOLID = 3
} D3D11_FILL_MODE;

// Keep PRIMITIVE_TOPOLOGY values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_PRIMITIVE_TOPOLOGY
{
    D3D11_PRIMITIVE_TOPOLOGY_UNDEFINED = 0,
    D3D11_PRIMITIVE_TOPOLOGY_POINTLIST = 1,
    D3D11_PRIMITIVE_TOPOLOGY_LINELIST = 2,
    D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP = 3,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST = 4,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP = 5,
    // 6 is reserved for legacy triangle fans
    // Adjacency values should be equal to (0x8 & non-adjacency):
    D3D11_PRIMITIVE_TOPOLOGY_LINELIST_ADJ = 10,
    D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ = 11,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ = 12,
    D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ = 13,
    D3D11_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST = 33,
    D3D11_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST = 34,
    D3D11_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST = 35,
    D3D11_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST = 36,
    D3D11_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST = 37,
    D3D11_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST = 38,
    D3D11_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST = 39,
    D3D11_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST = 40,
    D3D11_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST = 41,
    D3D11_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST = 42,
    D3D11_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST = 43,
    D3D11_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST = 44,
    D3D11_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST = 45,
    D3D11_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST = 46,
    D3D11_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST = 47,
    D3D11_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST = 48,
    D3D11_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST = 49,
    D3D11_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST = 50,
    D3D11_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST = 51,
    D3D11_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST = 52,
    D3D11_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST = 53,
    D3D11_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST = 54,
    D3D11_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST = 55,
    D3D11_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST = 56,
    D3D11_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST = 57,
    D3D11_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST = 58,
    D3D11_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST = 59,
    D3D11_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST = 60,
    D3D11_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST = 61,
    D3D11_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST = 62,
    D3D11_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST = 63,
    D3D11_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST = 64,
} D3D11_PRIMITIVE_TOPOLOGY;

typedef enum D3D11_PRIMITIVE
{
    D3D11_PRIMITIVE_UNDEFINED = 0,
    D3D11_PRIMITIVE_POINT = 1,
    D3D11_PRIMITIVE_LINE = 2,
    D3D11_PRIMITIVE_TRIANGLE = 3,
    // Adjacency values should be equal to (0x4 & non-adjacency):
    D3D11_PRIMITIVE_LINE_ADJ = 6,
    D3D11_PRIMITIVE_TRIANGLE_ADJ = 7,
    D3D11_PRIMITIVE_1_CONTROL_POINT_PATCH = 8,
    D3D11_PRIMITIVE_2_CONTROL_POINT_PATCH = 9,
    D3D11_PRIMITIVE_3_CONTROL_POINT_PATCH = 10,
    D3D11_PRIMITIVE_4_CONTROL_POINT_PATCH = 11,
    D3D11_PRIMITIVE_5_CONTROL_POINT_PATCH = 12,
    D3D11_PRIMITIVE_6_CONTROL_POINT_PATCH = 13,
    D3D11_PRIMITIVE_7_CONTROL_POINT_PATCH = 14,
    D3D11_PRIMITIVE_8_CONTROL_POINT_PATCH = 15,
    D3D11_PRIMITIVE_9_CONTROL_POINT_PATCH = 16,
    D3D11_PRIMITIVE_10_CONTROL_POINT_PATCH = 17,
    D3D11_PRIMITIVE_11_CONTROL_POINT_PATCH = 18,
    D3D11_PRIMITIVE_12_CONTROL_POINT_PATCH = 19,
    D3D11_PRIMITIVE_13_CONTROL_POINT_PATCH = 20,
    D3D11_PRIMITIVE_14_CONTROL_POINT_PATCH = 21,
    D3D11_PRIMITIVE_15_CONTROL_POINT_PATCH = 22,
    D3D11_PRIMITIVE_16_CONTROL_POINT_PATCH = 23,
    D3D11_PRIMITIVE_17_CONTROL_POINT_PATCH = 24,
    D3D11_PRIMITIVE_18_CONTROL_POINT_PATCH = 25,
    D3D11_PRIMITIVE_19_CONTROL_POINT_PATCH = 26,
    D3D11_PRIMITIVE_20_CONTROL_POINT_PATCH = 27,
    D3D11_PRIMITIVE_21_CONTROL_POINT_PATCH = 28,
    D3D11_PRIMITIVE_22_CONTROL_POINT_PATCH = 29,
    D3D11_PRIMITIVE_23_CONTROL_POINT_PATCH = 30,
    D3D11_PRIMITIVE_24_CONTROL_POINT_PATCH = 31,
    D3D11_PRIMITIVE_25_CONTROL_POINT_PATCH = 32,
    D3D11_PRIMITIVE_26_CONTROL_POINT_PATCH = 33,
    D3D11_PRIMITIVE_27_CONTROL_POINT_PATCH = 34,
    D3D11_PRIMITIVE_28_CONTROL_POINT_PATCH = 35,
    D3D11_PRIMITIVE_29_CONTROL_POINT_PATCH = 36,
    D3D11_PRIMITIVE_30_CONTROL_POINT_PATCH = 37,
    D3D11_PRIMITIVE_31_CONTROL_POINT_PATCH = 38,
    D3D11_PRIMITIVE_32_CONTROL_POINT_PATCH = 39,
} D3D11_PRIMITIVE;

// Keep CULL_MODE values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_CULL_MODE
{
    D3D11_CULL_NONE = 1,
    D3D11_CULL_FRONT = 2,
    D3D11_CULL_BACK = 3
} D3D11_CULL_MODE;

typedef struct D3D11_SO_DECLARATION_ENTRY
{
    UINT Stream;
    LPCSTR SemanticName;
    UINT SemanticIndex;
    BYTE StartComponent;
    BYTE ComponentCount;
    BYTE OutputSlot;
} D3D11_SO_DECLARATION_ENTRY;

typedef struct D3D11_VIEWPORT
{
    FLOAT TopLeftX;
    FLOAT TopLeftY;
    FLOAT Width;
    FLOAT Height;
    FLOAT MinDepth;
    FLOAT MaxDepth;
} D3D11_VIEWPORT;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "inline bool operator==( const D3D11_VIEWPORT& l, const D3D11_VIEWPORT& r )" )
cpp_quote( "{" )
cpp_quote( "    return l.TopLeftX == r.TopLeftX && l.TopLeftY == r.TopLeftY && l.Width == r.Width &&" )
cpp_quote( "        l.Height == r.Height && l.MinDepth == r.MinDepth && l.MaxDepth == r.MaxDepth;" )
cpp_quote( "}" )
cpp_quote( "inline bool operator!=( const D3D11_VIEWPORT& l, const D3D11_VIEWPORT& r )" )
cpp_quote( "{ return !( l == r ); }" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

typedef enum D3D11_RESOURCE_DIMENSION
{
    D3D11_RESOURCE_DIMENSION_UNKNOWN = 0,
    D3D11_RESOURCE_DIMENSION_BUFFER = 1,
    D3D11_RESOURCE_DIMENSION_TEXTURE1D = 2,
    D3D11_RESOURCE_DIMENSION_TEXTURE2D = 3,
    D3D11_RESOURCE_DIMENSION_TEXTURE3D = 4,
} D3D11_RESOURCE_DIMENSION;

typedef enum D3D11_SRV_DIMENSION
{
    D3D11_SRV_DIMENSION_UNKNOWN = 0,
    D3D11_SRV_DIMENSION_BUFFER = 1,
    D3D11_SRV_DIMENSION_TEXTURE1D = 2,
    D3D11_SRV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D11_SRV_DIMENSION_TEXTURE2D = 4,
    D3D11_SRV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D11_SRV_DIMENSION_TEXTURE2DMS = 6,
    D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D11_SRV_DIMENSION_TEXTURE3D = 8,
    D3D11_SRV_DIMENSION_TEXTURECUBE = 9,
    D3D11_SRV_DIMENSION_TEXTURECUBEARRAY = 10,
    D3D11_SRV_DIMENSION_BUFFEREX = 11,
} D3D11_SRV_DIMENSION;

typedef enum D3D11_DSV_DIMENSION
{
    D3D11_DSV_DIMENSION_UNKNOWN = 0,
    D3D11_DSV_DIMENSION_TEXTURE1D = 1,
    D3D11_DSV_DIMENSION_TEXTURE1DARRAY = 2,
    D3D11_DSV_DIMENSION_TEXTURE2D = 3,
    D3D11_DSV_DIMENSION_TEXTURE2DARRAY = 4,
    D3D11_DSV_DIMENSION_TEXTURE2DMS = 5,
    D3D11_DSV_DIMENSION_TEXTURE2DMSARRAY = 6,
} D3D11_DSV_DIMENSION;

typedef enum D3D11_RTV_DIMENSION
{
    D3D11_RTV_DIMENSION_UNKNOWN = 0,
    D3D11_RTV_DIMENSION_BUFFER = 1,
    D3D11_RTV_DIMENSION_TEXTURE1D = 2,
    D3D11_RTV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D11_RTV_DIMENSION_TEXTURE2D = 4,
    D3D11_RTV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D11_RTV_DIMENSION_TEXTURE2DMS = 6,
    D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D11_RTV_DIMENSION_TEXTURE3D = 8,
} D3D11_RTV_DIMENSION;

typedef enum D3D11_UAV_DIMENSION
{
    D3D11_UAV_DIMENSION_UNKNOWN = 0,
    D3D11_UAV_DIMENSION_BUFFER = 1,
    D3D11_UAV_DIMENSION_TEXTURE1D = 2,
    D3D11_UAV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D11_UAV_DIMENSION_TEXTURE2D = 4,
    D3D11_UAV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D11_UAV_DIMENSION_TEXTURE3D = 8,
} D3D11_UAV_DIMENSION;
 
typedef enum D3D11_USAGE
{
    D3D11_USAGE_DEFAULT = 0,
    D3D11_USAGE_IMMUTABLE = 1,
    D3D11_USAGE_DYNAMIC = 2,
    D3D11_USAGE_STAGING = 3,
} D3D11_USAGE;

typedef enum D3D11_BIND_FLAG
{
    D3D11_BIND_VERTEX_BUFFER = 0x00000001L,
    D3D11_BIND_INDEX_BUFFER = 0x00000002L,
    D3D11_BIND_CONSTANT_BUFFER = 0x00000004L,
    D3D11_BIND_SHADER_RESOURCE = 0x00000008L,
    D3D11_BIND_STREAM_OUTPUT = 0x00000010L,
    D3D11_BIND_RENDER_TARGET = 0x00000020L,
    D3D11_BIND_DEPTH_STENCIL = 0x00000040L,
    D3D11_BIND_UNORDERED_ACCESS = 0x00000080L,
} D3D11_BIND_FLAG;

typedef enum D3D11_CPU_ACCESS_FLAG
{
    D3D11_CPU_ACCESS_WRITE = 0x00010000L,
    D3D11_CPU_ACCESS_READ = 0x00020000L,
} D3D11_CPU_ACCESS_FLAG;

typedef enum D3D11_RESOURCE_MISC_FLAG
{
    D3D11_RESOURCE_MISC_GENERATE_MIPS          = 0x00000001L,
    D3D11_RESOURCE_MISC_SHARED                 = 0x00000002L,
    D3D11_RESOURCE_MISC_TEXTURECUBE            = 0x00000004L,
    D3D11_RESOURCE_MISC_DRAWINDIRECT_ARGS      = 0x00000010L,
    D3D11_RESOURCE_MISC_BUFFER_ALLOW_RAW_VIEWS = 0x00000020L,
    D3D11_RESOURCE_MISC_BUFFER_STRUCTURED      = 0x00000040L,
    D3D11_RESOURCE_MISC_RESOURCE_CLAMP         = 0x00000080L,
    D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX      = 0x00000100L,
    D3D11_RESOURCE_MISC_GDI_COMPATIBLE         = 0x00000200L,
} D3D11_RESOURCE_MISC_FLAG;

typedef enum D3D11_MAP // for calling ID3D11Resource::Map()
{
    D3D11_MAP_READ = 1,
    D3D11_MAP_WRITE = 2,
    D3D11_MAP_READ_WRITE = 3,
    D3D11_MAP_WRITE_DISCARD = 4,
    D3D11_MAP_WRITE_NO_OVERWRITE = 5,
} D3D11_MAP;

typedef enum D3D11_MAP_FLAG
{
    D3D11_MAP_FLAG_DO_NOT_WAIT = 0x00100000L,
} D3D11_MAP_FLAG;

typedef enum D3D11_RAISE_FLAG
{
    D3D11_RAISE_FLAG_DRIVER_INTERNAL_ERROR = 0x1L,
} D3D11_RAISE_FLAG;

// Flags for ClearDepthStencil
typedef enum D3D11_CLEAR_FLAG
{
    D3D11_CLEAR_DEPTH = 0x01L,
    D3D11_CLEAR_STENCIL = 0x02L,
} D3D11_CLEAR_FLAG;

typedef RECT D3D11_RECT;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_RECT : public D3D11_RECT" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_RECT()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_RECT( const D3D11_RECT& o ) :" )
cpp_quote( "        D3D11_RECT( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_RECT(" )
cpp_quote( "        LONG Left," )
cpp_quote( "        LONG Top," )
cpp_quote( "        LONG Right," )
cpp_quote( "        LONG Bottom )" )
cpp_quote( "    {" )
cpp_quote( "        left = Left;" )
cpp_quote( "        top = Top;" )
cpp_quote( "        right = Right;" )
cpp_quote( "        bottom = Bottom;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_RECT() {}" )
cpp_quote( "    operator const D3D11_RECT&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "inline bool operator==( const D3D11_RECT& l, const D3D11_RECT& r )" )
cpp_quote( "{" )
cpp_quote( "    return l.left == r.left && l.top == r.top &&" )
cpp_quote( "        l.right == r.right && l.bottom == r.bottom;" )
cpp_quote( "}" )
cpp_quote( "inline bool operator!=( const D3D11_RECT& l, const D3D11_RECT& r )" )
cpp_quote( "{ return !( l == r ); }" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

typedef struct D3D11_BOX
{
    UINT left;
    UINT top;
    UINT front;
    UINT right;
    UINT bottom;
    UINT back;
} D3D11_BOX;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_BOX : public D3D11_BOX" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_BOX()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_BOX( const D3D11_BOX& o ) :" )
cpp_quote( "        D3D11_BOX( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_BOX(" )
cpp_quote( "        LONG Left," )
cpp_quote( "        LONG Top," )
cpp_quote( "        LONG Front," )
cpp_quote( "        LONG Right," )
cpp_quote( "        LONG Bottom," )
cpp_quote( "        LONG Back )" )
cpp_quote( "    {" )
cpp_quote( "        left = Left;" )
cpp_quote( "        top = Top;" )
cpp_quote( "        front = Front;" )
cpp_quote( "        right = Right;" )
cpp_quote( "        bottom = Bottom;" )
cpp_quote( "        back = Back;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_BOX() {}" )
cpp_quote( "    operator const D3D11_BOX&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "inline bool operator==( const D3D11_BOX& l, const D3D11_BOX& r )" )
cpp_quote( "{" )
cpp_quote( "    return l.left == r.left && l.top == r.top && l.front == r.front &&" )
cpp_quote( "        l.right == r.right && l.bottom == r.bottom && l.back == r.back;" )
cpp_quote( "}" )
cpp_quote( "inline bool operator!=( const D3D11_BOX& l, const D3D11_BOX& r )" )
cpp_quote( "{ return !( l == r ); }" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

// Forward declarations:
interface ID3D11Device;
interface ID3D11ClassLinkage;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// DeviceChild
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

[ uuid( 1841e5c8-16b0-489b-bcc8-44cfb0d5deae ), object, local, pointer_default( unique ) ]
interface ID3D11DeviceChild
    : IUnknown
{
    void GetDevice( [annotation("__out")] ID3D11Device** ppDevice );
    HRESULT GetPrivateData(
        [annotation("__in")] REFGUID guid,
        [annotation("__inout")] UINT* pDataSize,
        [annotation("__out_bcount_opt( *pDataSize )")] void* pData );
    HRESULT SetPrivateData(
        [annotation("__in")] REFGUID guid,
        [annotation("__in")] UINT DataSize,
        [annotation("__in_bcount_opt( DataSize )")] const void* pData );
    HRESULT SetPrivateDataInterface(
        [annotation("__in")] REFGUID guid,
        [annotation("__in_opt")] const IUnknown * pData );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Depth-Stencil State
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Keep COMPARISON_FUNC values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_COMPARISON_FUNC
{
    D3D11_COMPARISON_NEVER = 1,
    D3D11_COMPARISON_LESS = 2,
    D3D11_COMPARISON_EQUAL = 3,
    D3D11_COMPARISON_LESS_EQUAL = 4,
    D3D11_COMPARISON_GREATER = 5,
    D3D11_COMPARISON_NOT_EQUAL = 6,
    D3D11_COMPARISON_GREATER_EQUAL = 7,
    D3D11_COMPARISON_ALWAYS = 8
} D3D11_COMPARISON_FUNC;

typedef enum D3D11_DEPTH_WRITE_MASK
{
    D3D11_DEPTH_WRITE_MASK_ZERO = 0,
    D3D11_DEPTH_WRITE_MASK_ALL = 1
} D3D11_DEPTH_WRITE_MASK;

// Keep STENCILOP values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_STENCIL_OP
{
    D3D11_STENCIL_OP_KEEP = 1,
    D3D11_STENCIL_OP_ZERO = 2,
    D3D11_STENCIL_OP_REPLACE = 3,
    D3D11_STENCIL_OP_INCR_SAT = 4,
    D3D11_STENCIL_OP_DECR_SAT = 5,
    D3D11_STENCIL_OP_INVERT = 6,
    D3D11_STENCIL_OP_INCR = 7,
    D3D11_STENCIL_OP_DECR = 8
} D3D11_STENCIL_OP;

typedef struct D3D11_DEPTH_STENCILOP_DESC
{
    D3D11_STENCIL_OP StencilFailOp;
    D3D11_STENCIL_OP StencilDepthFailOp;
    D3D11_STENCIL_OP StencilPassOp;
    D3D11_COMPARISON_FUNC StencilFunc;
} D3D11_DEPTH_STENCILOP_DESC;

typedef struct D3D11_DEPTH_STENCIL_DESC
{
    BOOL DepthEnable;
    D3D11_DEPTH_WRITE_MASK DepthWriteMask;
    D3D11_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D11_DEPTH_STENCILOP_DESC FrontFace;
    D3D11_DEPTH_STENCILOP_DESC BackFace;
} D3D11_DEPTH_STENCIL_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_DEPTH_STENCIL_DESC : public D3D11_DEPTH_STENCIL_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_DEPTH_STENCIL_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_DESC( const D3D11_DEPTH_STENCIL_DESC& o ) :" )
cpp_quote( "        D3D11_DEPTH_STENCIL_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_DESC( CD3D11_DEFAULT )" )
cpp_quote( "    {" )
cpp_quote( "        DepthEnable = TRUE;" )
cpp_quote( "        DepthWriteMask = D3D11_DEPTH_WRITE_MASK_ALL;" )
cpp_quote( "        DepthFunc = D3D11_COMPARISON_LESS;" )
cpp_quote( "        StencilEnable = FALSE;" )
cpp_quote( "        StencilReadMask = D3D11_DEFAULT_STENCIL_READ_MASK;" )
cpp_quote( "        StencilWriteMask = D3D11_DEFAULT_STENCIL_WRITE_MASK;" )
cpp_quote( "        const D3D11_DEPTH_STENCILOP_DESC defaultStencilOp =" )
cpp_quote( "        { D3D11_STENCIL_OP_KEEP, D3D11_STENCIL_OP_KEEP, D3D11_STENCIL_OP_KEEP, D3D11_COMPARISON_ALWAYS };" )
cpp_quote( "        FrontFace = defaultStencilOp;" )
cpp_quote( "        BackFace = defaultStencilOp;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_DESC(" )
cpp_quote( "        BOOL depthEnable," )
cpp_quote( "        D3D11_DEPTH_WRITE_MASK depthWriteMask," )
cpp_quote( "        D3D11_COMPARISON_FUNC depthFunc," )
cpp_quote( "        BOOL stencilEnable," )
cpp_quote( "        UINT8 stencilReadMask," )
cpp_quote( "        UINT8 stencilWriteMask," )
cpp_quote( "        D3D11_STENCIL_OP frontStencilFailOp," )
cpp_quote( "        D3D11_STENCIL_OP frontStencilDepthFailOp," )
cpp_quote( "        D3D11_STENCIL_OP frontStencilPassOp," )
cpp_quote( "        D3D11_COMPARISON_FUNC frontStencilFunc," )
cpp_quote( "        D3D11_STENCIL_OP backStencilFailOp," )
cpp_quote( "        D3D11_STENCIL_OP backStencilDepthFailOp," )
cpp_quote( "        D3D11_STENCIL_OP backStencilPassOp," )
cpp_quote( "        D3D11_COMPARISON_FUNC backStencilFunc )" )
cpp_quote( "    {" )
cpp_quote( "        DepthEnable = depthEnable;" )
cpp_quote( "        DepthWriteMask = depthWriteMask;" )
cpp_quote( "        DepthFunc = depthFunc;" )
cpp_quote( "        StencilEnable = stencilEnable;" )
cpp_quote( "        StencilReadMask = stencilReadMask;" )
cpp_quote( "        StencilWriteMask = stencilWriteMask;" )
cpp_quote( "        FrontFace.StencilFailOp = frontStencilFailOp;" )
cpp_quote( "        FrontFace.StencilDepthFailOp = frontStencilDepthFailOp;" )
cpp_quote( "        FrontFace.StencilPassOp = frontStencilPassOp;" )
cpp_quote( "        FrontFace.StencilFunc = frontStencilFunc;" )
cpp_quote( "        BackFace.StencilFailOp = backStencilFailOp;" )
cpp_quote( "        BackFace.StencilDepthFailOp = backStencilDepthFailOp;" )
cpp_quote( "        BackFace.StencilPassOp = backStencilPassOp;" )
cpp_quote( "        BackFace.StencilFunc = backStencilFunc;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_DEPTH_STENCIL_DESC() {}" )
cpp_quote( "    operator const D3D11_DEPTH_STENCIL_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 03823efb-8d8f-4e1c-9aa2-f64bb2cbfdf1 ), object, local, pointer_default( unique ) ]
interface ID3D11DepthStencilState
    : ID3D11DeviceChild
{
    void GetDesc( [annotation("__out")] D3D11_DEPTH_STENCIL_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Blend State
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Keep BLEND values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_BLEND
{
    D3D11_BLEND_ZERO = 1,
    D3D11_BLEND_ONE = 2,
    D3D11_BLEND_SRC_COLOR = 3, // PS output oN.rgb (N is current RT being blended)
    D3D11_BLEND_INV_SRC_COLOR = 4, // 1.0f - PS output oN.rgb
    D3D11_BLEND_SRC_ALPHA = 5, // PS output oN.a
    D3D11_BLEND_INV_SRC_ALPHA = 6, // 1.0f - PS output oN.a
    D3D11_BLEND_DEST_ALPHA = 7, // RT(N).a (N is current RT being blended)
    D3D11_BLEND_INV_DEST_ALPHA = 8, // 1.0f - RT(N).a
    D3D11_BLEND_DEST_COLOR = 9, // RT(N).rgb
    D3D11_BLEND_INV_DEST_COLOR = 10,// 1.0f - RT(N).rgb
    D3D11_BLEND_SRC_ALPHA_SAT = 11,// (f,f,f,1), f = min(1 - RT(N).a, oN.a)
    // 12 reserved (was BOTHSRCALPHA)
    // 13 reserved (was BOTHSRCALPHA)
    D3D11_BLEND_BLEND_FACTOR = 14,
    D3D11_BLEND_INV_BLEND_FACTOR = 15,
    D3D11_BLEND_SRC1_COLOR = 16, // PS output o1.rgb
    D3D11_BLEND_INV_SRC1_COLOR = 17, // 1.0f - PS output o1.rgb
    D3D11_BLEND_SRC1_ALPHA = 18, // PS output o1.a
    D3D11_BLEND_INV_SRC1_ALPHA = 19, // 1.0f - PS output o1.a
} D3D11_BLEND;

// Keep BLENDOP values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D11_BLEND_OP
{
    D3D11_BLEND_OP_ADD = 1,
    D3D11_BLEND_OP_SUBTRACT = 2,
    D3D11_BLEND_OP_REV_SUBTRACT = 3,
    D3D11_BLEND_OP_MIN = 4, // min semantics are like min shader instruction
    D3D11_BLEND_OP_MAX = 5, // max semantics are like max shader instruction
} D3D11_BLEND_OP;

typedef enum D3D11_COLOR_WRITE_ENABLE
{
    D3D11_COLOR_WRITE_ENABLE_RED = 1,
    D3D11_COLOR_WRITE_ENABLE_GREEN = 2,
    D3D11_COLOR_WRITE_ENABLE_BLUE = 4,
    D3D11_COLOR_WRITE_ENABLE_ALPHA = 8,
    D3D11_COLOR_WRITE_ENABLE_ALL = (D3D11_COLOR_WRITE_ENABLE_RED|D3D11_COLOR_WRITE_ENABLE_GREEN|
        D3D11_COLOR_WRITE_ENABLE_BLUE|D3D11_COLOR_WRITE_ENABLE_ALPHA),
} D3D11_COLOR_WRITE_ENABLE;

typedef struct D3D11_RENDER_TARGET_BLEND_DESC
{
    BOOL BlendEnable;
    D3D11_BLEND SrcBlend;
    D3D11_BLEND DestBlend;
    D3D11_BLEND_OP BlendOp;
    D3D11_BLEND SrcBlendAlpha;
    D3D11_BLEND DestBlendAlpha;
    D3D11_BLEND_OP BlendOpAlpha;
    UINT8 RenderTargetWriteMask; // D3D11_COLOR_WRITE_ENABLE
} D3D11_RENDER_TARGET_BLEND_DESC;

typedef struct D3D11_BLEND_DESC
{
    BOOL AlphaToCoverageEnable; // relevant to multisample antialiasing only
    BOOL IndependentBlendEnable; // if FALSE, then replicate the first entry in RenderTarget array to other entries
    D3D11_RENDER_TARGET_BLEND_DESC RenderTarget[D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT];
} D3D11_BLEND_DESC;
cpp_quote( "/* Note, the array size for RenderTarget[] above is D3D11_SIMULTANEOUS_RENDERTARGET_COUNT. ")
cpp_quote( "   IDL processing/generation of this header replaces the define; this comment is merely explaining what happened. */" ) 

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_BLEND_DESC : public D3D11_BLEND_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_BLEND_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_BLEND_DESC( const D3D11_BLEND_DESC& o ) :" )
cpp_quote( "        D3D11_BLEND_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_BLEND_DESC( CD3D11_DEFAULT )" )
cpp_quote( "    {" )
cpp_quote( "        AlphaToCoverageEnable = FALSE;" )
cpp_quote( "        IndependentBlendEnable = FALSE;" )
cpp_quote( "        const D3D11_RENDER_TARGET_BLEND_DESC defaultRenderTargetBlendDesc =" )
cpp_quote( "        {" )
cpp_quote( "            FALSE," )
cpp_quote( "            D3D11_BLEND_ONE, D3D11_BLEND_ZERO, D3D11_BLEND_OP_ADD," )
cpp_quote( "            D3D11_BLEND_ONE, D3D11_BLEND_ZERO, D3D11_BLEND_OP_ADD," )
cpp_quote( "            D3D11_COLOR_WRITE_ENABLE_ALL," )
cpp_quote( "        };" )
cpp_quote( "        for (UINT i = 0; i < D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT; ++i)" )
cpp_quote( "            RenderTarget[ i ] = defaultRenderTargetBlendDesc;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_BLEND_DESC() {}" )
cpp_quote( "    operator const D3D11_BLEND_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 75b68faa-347d-4159-8f45-a0640f01cd9a ), object, local, pointer_default( unique ) ]
interface ID3D11BlendState
    : ID3D11DeviceChild
{
    void GetDesc( [annotation("__out")] D3D11_BLEND_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Rasterizer State
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

typedef struct D3D11_RASTERIZER_DESC
{
    D3D11_FILL_MODE FillMode;
    D3D11_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL ScissorEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
} D3D11_RASTERIZER_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_RASTERIZER_DESC : public D3D11_RASTERIZER_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_RASTERIZER_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_RASTERIZER_DESC( const D3D11_RASTERIZER_DESC& o ) :" )
cpp_quote( "        D3D11_RASTERIZER_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_RASTERIZER_DESC( CD3D11_DEFAULT )" )
cpp_quote( "    {" )
cpp_quote( "        FillMode = D3D11_FILL_SOLID;" )
cpp_quote( "        CullMode = D3D11_CULL_BACK;" )
cpp_quote( "        FrontCounterClockwise = FALSE;" )
cpp_quote( "        DepthBias = D3D11_DEFAULT_DEPTH_BIAS;" )
cpp_quote( "        DepthBiasClamp = D3D11_DEFAULT_DEPTH_BIAS_CLAMP;" )
cpp_quote( "        SlopeScaledDepthBias = D3D11_DEFAULT_SLOPE_SCALED_DEPTH_BIAS;" )
cpp_quote( "        DepthClipEnable = TRUE;" )
cpp_quote( "        ScissorEnable = FALSE;" )
cpp_quote( "        MultisampleEnable = FALSE;" )
cpp_quote( "        AntialiasedLineEnable = FALSE;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_RASTERIZER_DESC(" )
cpp_quote( "        D3D11_FILL_MODE fillMode," )
cpp_quote( "        D3D11_CULL_MODE cullMode," )
cpp_quote( "        BOOL frontCounterClockwise," )
cpp_quote( "        INT depthBias," )
cpp_quote( "        FLOAT depthBiasClamp," )
cpp_quote( "        FLOAT slopeScaledDepthBias," )
cpp_quote( "        BOOL depthClipEnable," )
cpp_quote( "        BOOL scissorEnable," )
cpp_quote( "        BOOL multisampleEnable," )
cpp_quote( "        BOOL antialiasedLineEnable )" )
cpp_quote( "    {" )
cpp_quote( "        FillMode = fillMode;" )
cpp_quote( "        CullMode = cullMode;" )
cpp_quote( "        FrontCounterClockwise = frontCounterClockwise;" )
cpp_quote( "        DepthBias = depthBias;" )
cpp_quote( "        DepthBiasClamp = depthBiasClamp;" )
cpp_quote( "        SlopeScaledDepthBias = slopeScaledDepthBias;" )
cpp_quote( "        DepthClipEnable = depthClipEnable;" )
cpp_quote( "        ScissorEnable = scissorEnable;" )
cpp_quote( "        MultisampleEnable = multisampleEnable;" )
cpp_quote( "        AntialiasedLineEnable = antialiasedLineEnable;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_RASTERIZER_DESC() {}" )
cpp_quote( "    operator const D3D11_RASTERIZER_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 9bb4ab81-ab1a-4d8f-b506-fc04200b6ee7 ), object, local, pointer_default( unique ) ]
interface ID3D11RasterizerState
    : ID3D11DeviceChild
{
    void GetDesc( [annotation("__out")] D3D11_RASTERIZER_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Resource
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "inline UINT D3D11CalcSubresource( UINT MipSlice, UINT ArraySlice, UINT MipLevels )" )
cpp_quote( "{ return MipSlice + ArraySlice * MipLevels; }" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

typedef struct D3D11_SUBRESOURCE_DATA
{
    const void *pSysMem;
    UINT SysMemPitch;
    UINT SysMemSlicePitch;
} D3D11_SUBRESOURCE_DATA;

typedef struct D3D11_MAPPED_SUBRESOURCE
{
    void *pData;
    UINT RowPitch;
    UINT DepthPitch;
} D3D11_MAPPED_SUBRESOURCE;

[ uuid( dc8e63f3-d12b-4952-b47b-5e45026a862d ), object, local, pointer_default( unique ) ]
interface ID3D11Resource
    : ID3D11DeviceChild
{
    void GetType( [annotation("__out")] D3D11_RESOURCE_DIMENSION* pResourceDimension );
    void SetEvictionPriority( [annotation("__in")] UINT EvictionPriority );
    UINT GetEvictionPriority();
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Buffer
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_BUFFER_DESC
{
    UINT ByteWidth;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    UINT StructureByteStride; // Stride of Structured Buffer; ignored if STRUCTURED MiscFlag not set
} D3D11_BUFFER_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_BUFFER_DESC : public D3D11_BUFFER_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_BUFFER_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_BUFFER_DESC( const D3D11_BUFFER_DESC& o ) :" )
cpp_quote( "        D3D11_BUFFER_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_BUFFER_DESC(" )
cpp_quote( "        UINT byteWidth," )
cpp_quote( "        UINT bindFlags," )
cpp_quote( "        D3D11_USAGE usage = D3D11_USAGE_DEFAULT," )
cpp_quote( "        UINT cpuaccessFlags = 0," )
cpp_quote( "        UINT miscFlags = 0," )
cpp_quote( "        UINT structureByteStride = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        ByteWidth = byteWidth;" )
cpp_quote( "        Usage = usage;" )
cpp_quote( "        BindFlags = bindFlags;" )
cpp_quote( "        CPUAccessFlags = cpuaccessFlags ;" )
cpp_quote( "        MiscFlags = miscFlags;" )
cpp_quote( "        StructureByteStride = structureByteStride;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_BUFFER_DESC() {}" )
cpp_quote( "    operator const D3D11_BUFFER_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 48570b85-d1ee-4fcd-a250-eb350722b037 ), object, local, pointer_default( unique ) ]
interface ID3D11Buffer
    : ID3D11Resource
{
    void GetDesc( [annotation("__out")] D3D11_BUFFER_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Texture1D
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_TEXTURE1D_DESC
{
    UINT Width;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE1D_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_TEXTURE1D_DESC : public D3D11_TEXTURE1D_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_TEXTURE1D_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_TEXTURE1D_DESC( const D3D11_TEXTURE1D_DESC& o ) :" )
cpp_quote( "        D3D11_TEXTURE1D_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_TEXTURE1D_DESC(" )
cpp_quote( "        DXGI_FORMAT format," )
cpp_quote( "        UINT width," )
cpp_quote( "        UINT arraySize = 1," )
cpp_quote( "        UINT mipLevels = 0," )
cpp_quote( "        UINT bindFlags = D3D11_BIND_SHADER_RESOURCE," )
cpp_quote( "        D3D11_USAGE usage = D3D11_USAGE_DEFAULT," )
cpp_quote( "        UINT cpuaccessFlags= 0," )
cpp_quote( "        UINT miscFlags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Width = width;" )
cpp_quote( "        MipLevels = mipLevels;" )
cpp_quote( "        ArraySize = arraySize;" )
cpp_quote( "        Format = format;" )
cpp_quote( "        Usage = usage;" )
cpp_quote( "        BindFlags = bindFlags;" )
cpp_quote( "        CPUAccessFlags = cpuaccessFlags;" )
cpp_quote( "        MiscFlags = miscFlags;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_TEXTURE1D_DESC() {}" )
cpp_quote( "    operator const D3D11_TEXTURE1D_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( f8fb5c27-c6b3-4f75-a4c8-439af2ef564c ), object, local, pointer_default( unique ) ]
interface ID3D11Texture1D
    : ID3D11Resource
{
    void GetDesc( [annotation("__out")] D3D11_TEXTURE1D_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Texture2D
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_TEXTURE2D_DESC
{
    UINT Width;
    UINT Height;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE2D_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_TEXTURE2D_DESC : public D3D11_TEXTURE2D_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_TEXTURE2D_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_TEXTURE2D_DESC( const D3D11_TEXTURE2D_DESC& o ) :" )
cpp_quote( "        D3D11_TEXTURE2D_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_TEXTURE2D_DESC(" )
cpp_quote( "        DXGI_FORMAT format," )
cpp_quote( "        UINT width," )
cpp_quote( "        UINT height," )
cpp_quote( "        UINT arraySize = 1," )
cpp_quote( "        UINT mipLevels = 0," )
cpp_quote( "        UINT bindFlags = D3D11_BIND_SHADER_RESOURCE," )
cpp_quote( "        D3D11_USAGE usage = D3D11_USAGE_DEFAULT," )
cpp_quote( "        UINT cpuaccessFlags = 0," )
cpp_quote( "        UINT sampleCount = 1," )
cpp_quote( "        UINT sampleQuality = 0," )
cpp_quote( "        UINT miscFlags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Width = width;" )
cpp_quote( "        Height = height;" )
cpp_quote( "        MipLevels = mipLevels;" )
cpp_quote( "        ArraySize = arraySize;" )
cpp_quote( "        Format = format;" )
cpp_quote( "        SampleDesc.Count = sampleCount;" )
cpp_quote( "        SampleDesc.Quality = sampleQuality;" )
cpp_quote( "        Usage = usage;" )
cpp_quote( "        BindFlags = bindFlags;" )
cpp_quote( "        CPUAccessFlags = cpuaccessFlags;" )
cpp_quote( "        MiscFlags = miscFlags;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_TEXTURE2D_DESC() {}" )
cpp_quote( "    operator const D3D11_TEXTURE2D_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 6f15aaf2-d208-4e89-9ab4-489535d34f9c ), object, local, pointer_default( unique ) ]
interface ID3D11Texture2D
    : ID3D11Resource
{
    void GetDesc( [annotation("__out")] D3D11_TEXTURE2D_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Texture3D
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_TEXTURE3D_DESC
{
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
} D3D11_TEXTURE3D_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_TEXTURE3D_DESC : public D3D11_TEXTURE3D_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_TEXTURE3D_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_TEXTURE3D_DESC( const D3D11_TEXTURE3D_DESC& o ) :" )
cpp_quote( "        D3D11_TEXTURE3D_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_TEXTURE3D_DESC(" )
cpp_quote( "        DXGI_FORMAT format," )
cpp_quote( "        UINT width," )
cpp_quote( "        UINT height," )
cpp_quote( "        UINT depth," )
cpp_quote( "        UINT mipLevels = 0," )
cpp_quote( "        UINT bindFlags = D3D11_BIND_SHADER_RESOURCE," )
cpp_quote( "        D3D11_USAGE usage = D3D11_USAGE_DEFAULT," )
cpp_quote( "        UINT cpuaccessFlags = 0," )
cpp_quote( "        UINT miscFlags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Width = width;" )
cpp_quote( "        Height = height;" )
cpp_quote( "        Depth = depth;" )
cpp_quote( "        MipLevels = mipLevels;" )
cpp_quote( "        Format = format;" )
cpp_quote( "        Usage = usage;" )
cpp_quote( "        BindFlags = bindFlags;" )
cpp_quote( "        CPUAccessFlags = cpuaccessFlags;" )
cpp_quote( "        MiscFlags = miscFlags;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_TEXTURE3D_DESC() {}" )
cpp_quote( "    operator const D3D11_TEXTURE3D_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 037e866e-f56d-4357-a8af-9dabbe6e250e ), object, local, pointer_default( unique ) ]
interface ID3D11Texture3D
    : ID3D11Resource
{
    void GetDesc( [annotation("__out")] D3D11_TEXTURE3D_DESC* pDesc );
};

/* TextureCube Face identifiers */
typedef enum D3D11_TEXTURECUBE_FACE
{
    D3D11_TEXTURECUBE_FACE_POSITIVE_X = 0,
    D3D11_TEXTURECUBE_FACE_NEGATIVE_X = 1,
    D3D11_TEXTURECUBE_FACE_POSITIVE_Y = 2,
    D3D11_TEXTURECUBE_FACE_NEGATIVE_Y = 3,
    D3D11_TEXTURECUBE_FACE_POSITIVE_Z = 4,
    D3D11_TEXTURECUBE_FACE_NEGATIVE_Z = 5
} D3D11_TEXTURECUBE_FACE;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// View
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( 839d1216-bb2e-412b-b7f4-a9dbebe08ed1 ), object, local, pointer_default( unique ) ]
interface ID3D11View
    : ID3D11DeviceChild
{
    void GetResource( [annotation("__out")] ID3D11Resource** ppResource );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// ShaderResourceView
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_BUFFER_SRV
{
    union
    {
        UINT FirstElement;  // Nicer name
        UINT ElementOffset;
    };
    union
    {
        UINT NumElements;   // Nicer name
        UINT ElementWidth;  
    };
} D3D11_BUFFER_SRV;

typedef enum D3D11_BUFFEREX_SRV_FLAG
{
    D3D11_BUFFEREX_SRV_FLAG_RAW = 0x00000001,      // allow device multi-component reads with DWORD addressing
} D3D11_BUFFEREX_SRV_FLAG;

typedef struct D3D11_BUFFEREX_SRV
{
    UINT FirstElement;
    UINT NumElements;
    UINT Flags;
} D3D11_BUFFEREX_SRV;

typedef struct D3D11_TEX1D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
 } D3D11_TEX1D_SRV;

typedef struct D3D11_TEX1D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_SRV;

typedef struct D3D11_TEX2D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX2D_SRV;

typedef struct D3D11_TEX2D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_SRV;

typedef struct D3D11_TEX3D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEX3D_SRV;

typedef struct D3D11_TEXCUBE_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
} D3D11_TEXCUBE_SRV;

typedef struct D3D11_TEXCUBE_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;  
    UINT NumCubes;
} D3D11_TEXCUBE_ARRAY_SRV;

typedef struct D3D11_TEX2DMS_SRV
{
    // don't need to define anything specific for this view dimension
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_SRV;

typedef struct D3D11_TEX2DMS_ARRAY_SRV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_SRV;

typedef struct D3D11_SHADER_RESOURCE_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_SRV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_SRV Buffer;
        D3D11_TEX1D_SRV Texture1D;
        D3D11_TEX1D_ARRAY_SRV Texture1DArray;
        D3D11_TEX2D_SRV Texture2D;
        D3D11_TEX2D_ARRAY_SRV Texture2DArray;
        D3D11_TEX2DMS_SRV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D11_TEX3D_SRV Texture3D;
        D3D11_TEXCUBE_SRV TextureCube;
        D3D11_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D11_BUFFEREX_SRV BufferEx;
    };
} D3D11_SHADER_RESOURCE_VIEW_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_SHADER_RESOURCE_VIEW_DESC : public D3D11_SHADER_RESOURCE_VIEW_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_SHADER_RESOURCE_VIEW_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC( const D3D11_SHADER_RESOURCE_VIEW_DESC& o ) :" )
cpp_quote( "        D3D11_SHADER_RESOURCE_VIEW_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(" )
cpp_quote( "        D3D11_SRV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mostDetailedMip = 0, // FirstElement for BUFFER" )
cpp_quote( "        UINT mipLevels = -1, // NumElements for BUFFER" )
cpp_quote( "        UINT firstArraySlice = 0, // First2DArrayFace for TEXTURECUBEARRAY" )
cpp_quote( "        UINT arraySize = -1, // NumCubes for TEXTURECUBEARRAY" )
cpp_quote( "        UINT flags = 0 ) // BUFFEREX only" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_SRV_DIMENSION_BUFFER:" )
cpp_quote( "            Buffer.FirstElement = mostDetailedMip;" )
cpp_quote( "            Buffer.NumElements = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture1D.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture1DArray.MipLevels = mipLevels;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture2D.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture2DArray.MipLevels = mipLevels;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            Texture2DMSArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DMSArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE3D:" )
cpp_quote( "            Texture3D.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture3D.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURECUBE:" )
cpp_quote( "            TextureCube.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            TextureCube.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURECUBEARRAY:" )
cpp_quote( "            TextureCubeArray.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            TextureCubeArray.MipLevels = mipLevels;" )
cpp_quote( "            TextureCubeArray.First2DArrayFace = firstArraySlice;" )
cpp_quote( "            TextureCubeArray.NumCubes = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_BUFFEREX:" )
cpp_quote( "            BufferEx.FirstElement = mostDetailedMip;" )
cpp_quote( "            BufferEx.NumElements = mipLevels;" )
cpp_quote( "            BufferEx.Flags = flags;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Buffer*," )
cpp_quote( "        DXGI_FORMAT format," )
cpp_quote( "        UINT firstElement," )
cpp_quote( "        UINT numElements," )
cpp_quote( "        UINT flags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = D3D11_SRV_DIMENSION_BUFFEREX;" )
cpp_quote( "        BufferEx.FirstElement = firstElement;" )
cpp_quote( "        BufferEx.NumElements = numElements;" )
cpp_quote( "        BufferEx.Flags = flags;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture1D* pTex1D," )
cpp_quote( "        D3D11_SRV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mostDetailedMip = 0," )
cpp_quote( "        UINT mipLevels = -1," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || -1 == mipLevels ||" )
cpp_quote( "            (-1 == arraySize && D3D11_SRV_DIMENSION_TEXTURE1DARRAY == viewDimension))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE1D_DESC TexDesc;" )
cpp_quote( "            pTex1D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == mipLevels) mipLevels = TexDesc.MipLevels - mostDetailedMip;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture1D.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture1DArray.MipLevels = mipLevels;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture2D* pTex2D," )
cpp_quote( "        D3D11_SRV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mostDetailedMip = 0," )
cpp_quote( "        UINT mipLevels = -1," )
cpp_quote( "        UINT firstArraySlice = 0, // First2DArrayFace for TEXTURECUBEARRAY" )
cpp_quote( "        UINT arraySize = -1 ) // NumCubes for TEXTURECUBEARRAY" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || " )
cpp_quote( "            (-1 == mipLevels &&" )
cpp_quote( "                D3D11_SRV_DIMENSION_TEXTURE2DMS != viewDimension &&" )
cpp_quote( "                D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY != viewDimension) ||" )
cpp_quote( "            (-1 == arraySize &&" )
cpp_quote( "                (D3D11_SRV_DIMENSION_TEXTURE2DARRAY == viewDimension ||" )
cpp_quote( "                D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY == viewDimension ||" )
cpp_quote( "                D3D11_SRV_DIMENSION_TEXTURECUBEARRAY == viewDimension)))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE2D_DESC TexDesc;" )
cpp_quote( "            pTex2D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == mipLevels) mipLevels = TexDesc.MipLevels - mostDetailedMip;" )
cpp_quote( "            if (-1 == arraySize)" )
cpp_quote( "            {" )
cpp_quote( "                arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "                if (D3D11_SRV_DIMENSION_TEXTURECUBEARRAY == viewDimension) arraySize /= 6;" )
cpp_quote( "            }" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture2D.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            Texture2DArray.MipLevels = mipLevels;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            Texture2DMSArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DMSArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURECUBE:" )
cpp_quote( "            TextureCube.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            TextureCube.MipLevels = mipLevels;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_SRV_DIMENSION_TEXTURECUBEARRAY:" )
cpp_quote( "            TextureCubeArray.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "            TextureCubeArray.MipLevels = mipLevels;" )
cpp_quote( "            TextureCubeArray.First2DArrayFace = firstArraySlice;" )
cpp_quote( "            TextureCubeArray.NumCubes = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_SHADER_RESOURCE_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture3D* pTex3D," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mostDetailedMip = 0," )
cpp_quote( "        UINT mipLevels = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = D3D11_SRV_DIMENSION_TEXTURE3D;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || -1 == mipLevels)" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE3D_DESC TexDesc;" )
cpp_quote( "            pTex3D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == mipLevels) mipLevels = TexDesc.MipLevels - mostDetailedMip;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        Texture3D.MostDetailedMip = mostDetailedMip;" )
cpp_quote( "        Texture3D.MipLevels = mipLevels;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_SHADER_RESOURCE_VIEW_DESC() {}" )
cpp_quote( "    operator const D3D11_SHADER_RESOURCE_VIEW_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( b0e06fe0-8192-4e1a-b1ca-36d7414710b2 ), object, local, pointer_default( unique ) ]
interface ID3D11ShaderResourceView
    : ID3D11View
{
    void GetDesc( [annotation("__out")] D3D11_SHADER_RESOURCE_VIEW_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// RenderTargetView
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_BUFFER_RTV
{
    union
    {
        UINT FirstElement;  // Nicer name
        UINT ElementOffset;
    };
    union
    {
        UINT NumElements;   // Nicer name
        UINT ElementWidth; 
    };
} D3D11_BUFFER_RTV;

typedef struct D3D11_TEX1D_RTV
{
    UINT MipSlice;
} D3D11_TEX1D_RTV;

typedef struct D3D11_TEX1D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_RTV;

typedef struct D3D11_TEX2D_RTV
{
    UINT MipSlice;
} D3D11_TEX2D_RTV;

typedef struct D3D11_TEX2DMS_RTV
{
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_RTV;

typedef struct D3D11_TEX2D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_RTV;

typedef struct D3D11_TEX2DMS_ARRAY_RTV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_RTV;

typedef struct D3D11_TEX3D_RTV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D11_TEX3D_RTV;

typedef struct D3D11_RENDER_TARGET_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_RTV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_RTV Buffer;
        D3D11_TEX1D_RTV Texture1D;
        D3D11_TEX1D_ARRAY_RTV Texture1DArray;
        D3D11_TEX2D_RTV Texture2D;
        D3D11_TEX2D_ARRAY_RTV Texture2DArray;
        D3D11_TEX2DMS_RTV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D11_TEX3D_RTV Texture3D;
    };
} D3D11_RENDER_TARGET_VIEW_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_RENDER_TARGET_VIEW_DESC : public D3D11_RENDER_TARGET_VIEW_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_RENDER_TARGET_VIEW_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_RENDER_TARGET_VIEW_DESC( const D3D11_RENDER_TARGET_VIEW_DESC& o ) :" )
cpp_quote( "        D3D11_RENDER_TARGET_VIEW_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_RENDER_TARGET_VIEW_DESC(" )
cpp_quote( "        D3D11_RTV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0, // FirstElement for BUFFER" )
cpp_quote( "        UINT firstArraySlice = 0, // NumElements for BUFFER, FirstWSlice for TEXTURE3D" )
cpp_quote( "        UINT arraySize = -1 ) // WSize for TEXTURE3D" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_BUFFER:" )
cpp_quote( "            Buffer.FirstElement = mipSlice;" )
cpp_quote( "            Buffer.NumElements = firstArraySlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            Texture2DMSArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DMSArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE3D:" )
cpp_quote( "            Texture3D.MipSlice = mipSlice;" )
cpp_quote( "            Texture3D.FirstWSlice = firstArraySlice;" )
cpp_quote( "            Texture3D.WSize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_RENDER_TARGET_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Buffer*," )
cpp_quote( "        DXGI_FORMAT format," )
cpp_quote( "        UINT firstElement," )
cpp_quote( "        UINT numElements )" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = D3D11_RTV_DIMENSION_BUFFER;" )
cpp_quote( "        Buffer.FirstElement = firstElement;" )
cpp_quote( "        Buffer.NumElements = numElements;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_RENDER_TARGET_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture1D* pTex1D," )
cpp_quote( "        D3D11_RTV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format ||" )
cpp_quote( "            (-1 == arraySize && D3D11_RTV_DIMENSION_TEXTURE1DARRAY == viewDimension))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE1D_DESC TexDesc;" )
cpp_quote( "            pTex1D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_RENDER_TARGET_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture2D* pTex2D," )
cpp_quote( "        D3D11_RTV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || " )
cpp_quote( "            (-1 == arraySize &&" )
cpp_quote( "                (D3D11_RTV_DIMENSION_TEXTURE2DARRAY == viewDimension ||" )
cpp_quote( "                D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY == viewDimension)))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE2D_DESC TexDesc;" )
cpp_quote( "            pTex2D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            Texture2DMSArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DMSArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_RENDER_TARGET_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture3D* pTex3D," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstWSlice = 0," )
cpp_quote( "        UINT wSize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = D3D11_RTV_DIMENSION_TEXTURE3D;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || -1 == wSize)" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE3D_DESC TexDesc;" )
cpp_quote( "            pTex3D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == wSize) wSize = TexDesc.Depth - firstWSlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        Texture3D.MipSlice = mipSlice;" )
cpp_quote( "        Texture3D.FirstWSlice = firstWSlice;" )
cpp_quote( "        Texture3D.WSize = wSize;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_RENDER_TARGET_VIEW_DESC() {}" )
cpp_quote( "    operator const D3D11_RENDER_TARGET_VIEW_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( dfdba067-0b8d-4865-875b-d7b4516cc164 ), object, local, pointer_default( unique ) ]
interface ID3D11RenderTargetView
    : ID3D11View
{
    void GetDesc( [annotation("__out")] D3D11_RENDER_TARGET_VIEW_DESC* pDesc );
};

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_VIEWPORT : public D3D11_VIEWPORT" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_VIEWPORT()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_VIEWPORT( const D3D11_VIEWPORT& o ) :" )
cpp_quote( "        D3D11_VIEWPORT( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_VIEWPORT(" )
cpp_quote( "        FLOAT topLeftX," )
cpp_quote( "        FLOAT topLeftY," )
cpp_quote( "        FLOAT width," )
cpp_quote( "        FLOAT height," )
cpp_quote( "        FLOAT minDepth = D3D11_MIN_DEPTH," )
cpp_quote( "        FLOAT maxDepth = D3D11_MAX_DEPTH )" )
cpp_quote( "    {" )
cpp_quote( "        TopLeftX = topLeftX;" )
cpp_quote( "        TopLeftY = topLeftY;" )
cpp_quote( "        Width = width;" )
cpp_quote( "        Height = height;" )
cpp_quote( "        MinDepth = minDepth;" )
cpp_quote( "        MaxDepth = maxDepth;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_VIEWPORT(" )
cpp_quote( "        __in ID3D11Buffer*," )
cpp_quote( "        __in ID3D11RenderTargetView* pRTView," )
cpp_quote( "        FLOAT topLeftX = 0.0f," )
cpp_quote( "        FLOAT minDepth = D3D11_MIN_DEPTH," )
cpp_quote( "        FLOAT maxDepth = D3D11_MAX_DEPTH )" )
cpp_quote( "    {" )
cpp_quote( "        D3D11_RENDER_TARGET_VIEW_DESC RTVDesc;" )
cpp_quote( "        pRTView->GetDesc( &RTVDesc );" )
cpp_quote( "        UINT NumElements = 0;" )
cpp_quote( "        switch (RTVDesc.ViewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_BUFFER:" )
cpp_quote( "            NumElements = RTVDesc.Buffer.NumElements;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "        TopLeftX = topLeftX;" )
cpp_quote( "        TopLeftY = 0.0f;" )
cpp_quote( "        Width = NumElements - topLeftX;" )
cpp_quote( "        Height = 1.0f;" )
cpp_quote( "        MinDepth = minDepth;" )
cpp_quote( "        MaxDepth = maxDepth;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_VIEWPORT(" )
cpp_quote( "        __in ID3D11Texture1D* pTex1D," )
cpp_quote( "        __in ID3D11RenderTargetView* pRTView," )
cpp_quote( "        FLOAT topLeftX = 0.0f," )
cpp_quote( "        FLOAT minDepth = D3D11_MIN_DEPTH," )
cpp_quote( "        FLOAT maxDepth = D3D11_MAX_DEPTH )" )
cpp_quote( "    {" )
cpp_quote( "        D3D11_TEXTURE1D_DESC TexDesc;" )
cpp_quote( "        pTex1D->GetDesc( &TexDesc );" )
cpp_quote( "        D3D11_RENDER_TARGET_VIEW_DESC RTVDesc;" )
cpp_quote( "        pRTView->GetDesc( &RTVDesc );" )
cpp_quote( "        UINT MipSlice = 0;" )
cpp_quote( "        switch (RTVDesc.ViewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            MipSlice = RTVDesc.Texture1D.MipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            MipSlice = RTVDesc.Texture1DArray.MipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "        const UINT SubResourceWidth = TexDesc.Width / (UINT( 1 ) << MipSlice);" )
cpp_quote( "        TopLeftX = topLeftX;" )
cpp_quote( "        TopLeftY = 0.0f;" )
cpp_quote( "        Width = (SubResourceWidth ? SubResourceWidth : 1) - topLeftX;" )
cpp_quote( "        Height = 1.0f;" )
cpp_quote( "        MinDepth = minDepth;" )
cpp_quote( "        MaxDepth = maxDepth;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_VIEWPORT(" )
cpp_quote( "        __in ID3D11Texture2D* pTex2D," )
cpp_quote( "        __in ID3D11RenderTargetView* pRTView," )
cpp_quote( "        FLOAT topLeftX = 0.0f," )
cpp_quote( "        FLOAT topLeftY = 0.0f," )
cpp_quote( "        FLOAT minDepth = D3D11_MIN_DEPTH," )
cpp_quote( "        FLOAT maxDepth = D3D11_MAX_DEPTH )" )
cpp_quote( "    {" )
cpp_quote( "        D3D11_TEXTURE2D_DESC TexDesc;" )
cpp_quote( "        pTex2D->GetDesc( &TexDesc );" )
cpp_quote( "        D3D11_RENDER_TARGET_VIEW_DESC RTVDesc;" )
cpp_quote( "        pRTView->GetDesc( &RTVDesc );" )
cpp_quote( "        UINT MipSlice = 0;" )
cpp_quote( "        switch (RTVDesc.ViewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            MipSlice = RTVDesc.Texture2D.MipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            MipSlice = RTVDesc.Texture2DArray.MipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "        const UINT SubResourceWidth = TexDesc.Width / (UINT( 1 ) << MipSlice);" )
cpp_quote( "        const UINT SubResourceHeight = TexDesc.Height / (UINT( 1 ) << MipSlice);" )
cpp_quote( "        TopLeftX = topLeftX;" )
cpp_quote( "        TopLeftY = topLeftY;" )
cpp_quote( "        Width = (SubResourceWidth ? SubResourceWidth : 1) - topLeftX;" )
cpp_quote( "        Height = (SubResourceHeight ? SubResourceHeight : 1) - topLeftY;" )
cpp_quote( "        MinDepth = minDepth;" )
cpp_quote( "        MaxDepth = maxDepth;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_VIEWPORT(" )
cpp_quote( "        __in ID3D11Texture3D* pTex3D," )
cpp_quote( "        __in ID3D11RenderTargetView* pRTView," )
cpp_quote( "        FLOAT topLeftX = 0.0f," )
cpp_quote( "        FLOAT topLeftY = 0.0f," )
cpp_quote( "        FLOAT minDepth = D3D11_MIN_DEPTH," )
cpp_quote( "        FLOAT maxDepth = D3D11_MAX_DEPTH )" )
cpp_quote( "    {" )
cpp_quote( "        D3D11_TEXTURE3D_DESC TexDesc;" )
cpp_quote( "        pTex3D->GetDesc( &TexDesc );" )
cpp_quote( "        D3D11_RENDER_TARGET_VIEW_DESC RTVDesc;" )
cpp_quote( "        pRTView->GetDesc( &RTVDesc );" )
cpp_quote( "        UINT MipSlice = 0;" )
cpp_quote( "        switch (RTVDesc.ViewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_RTV_DIMENSION_TEXTURE3D:" )
cpp_quote( "            MipSlice = RTVDesc.Texture3D.MipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "        const UINT SubResourceWidth = TexDesc.Width / (UINT( 1 ) << MipSlice);" )
cpp_quote( "        const UINT SubResourceHeight = TexDesc.Height / (UINT( 1 ) << MipSlice);" )
cpp_quote( "        TopLeftX = topLeftX;" )
cpp_quote( "        TopLeftY = topLeftY;" )
cpp_quote( "        Width = (SubResourceWidth ? SubResourceWidth : 1) - topLeftX;" )
cpp_quote( "        Height = (SubResourceHeight ? SubResourceHeight : 1) - topLeftY;" )
cpp_quote( "        MinDepth = minDepth;" )
cpp_quote( "        MaxDepth = maxDepth;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_VIEWPORT() {}" )
cpp_quote( "    operator const D3D11_VIEWPORT&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// DepthStencilView
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct D3D11_TEX1D_DSV
{
    UINT MipSlice;
} D3D11_TEX1D_DSV;

typedef struct D3D11_TEX1D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_DSV;

typedef struct D3D11_TEX2D_DSV
{
    UINT MipSlice;
} D3D11_TEX2D_DSV;

typedef struct D3D11_TEX2D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_DSV;

typedef struct D3D11_TEX2DMS_DSV
{
    UINT UnusedField_NothingToDefine;
} D3D11_TEX2DMS_DSV;

typedef struct D3D11_TEX2DMS_ARRAY_DSV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2DMS_ARRAY_DSV;

typedef enum D3D11_DSV_FLAG
{
    D3D11_DSV_READ_ONLY_DEPTH = 0x1L,
    D3D11_DSV_READ_ONLY_STENCIL = 0x2L,
} D3D11_DSV_FLAG;

typedef struct D3D11_DEPTH_STENCIL_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_DSV_DIMENSION ViewDimension;
    UINT Flags; // D3D11_DSV_FLAG

    union
    {
        D3D11_TEX1D_DSV Texture1D;
        D3D11_TEX1D_ARRAY_DSV Texture1DArray;
        D3D11_TEX2D_DSV Texture2D;
        D3D11_TEX2D_ARRAY_DSV Texture2DArray;
        D3D11_TEX2DMS_DSV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_DSV Texture2DMSArray;
    };
} D3D11_DEPTH_STENCIL_VIEW_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_DEPTH_STENCIL_VIEW_DESC : public D3D11_DEPTH_STENCIL_VIEW_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_DEPTH_STENCIL_VIEW_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_VIEW_DESC( const D3D11_DEPTH_STENCIL_VIEW_DESC& o ) :" )
cpp_quote( "        D3D11_DEPTH_STENCIL_VIEW_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_VIEW_DESC(" )
cpp_quote( "        D3D11_DSV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1," )
cpp_quote( "        UINT flags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        Flags = flags;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            Texture2DMSArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DMSArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture1D* pTex1D," )
cpp_quote( "        D3D11_DSV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1," )
cpp_quote( "        UINT flags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        Flags = flags;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format ||" )
cpp_quote( "            (-1 == arraySize && D3D11_DSV_DIMENSION_TEXTURE1DARRAY == viewDimension))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE1D_DESC TexDesc;" )
cpp_quote( "            pTex1D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_DEPTH_STENCIL_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture2D* pTex2D," )
cpp_quote( "        D3D11_DSV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1," )
cpp_quote( "        UINT flags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        Flags = flags;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || " )
cpp_quote( "            (-1 == arraySize &&" )
cpp_quote( "                (D3D11_DSV_DIMENSION_TEXTURE2DARRAY == viewDimension ||" )
cpp_quote( "                D3D11_DSV_DIMENSION_TEXTURE2DMSARRAY == viewDimension)))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE2D_DESC TexDesc;" )
cpp_quote( "            pTex2D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2DMS:" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_DSV_DIMENSION_TEXTURE2DMSARRAY:" )
cpp_quote( "            Texture2DMSArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DMSArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_DEPTH_STENCIL_VIEW_DESC() {}" )
cpp_quote( "    operator const D3D11_DEPTH_STENCIL_VIEW_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 9fdac92a-1876-48c3-afad-25b94f84a9b6 ), object, local, pointer_default( unique ) ]
interface ID3D11DepthStencilView
    : ID3D11View
{
    void GetDesc( [annotation("__out")] D3D11_DEPTH_STENCIL_VIEW_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// UnorderedAccessView
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef enum D3D11_BUFFER_UAV_FLAG
{
    D3D11_BUFFER_UAV_FLAG_RAW =    0x00000001,
    D3D11_BUFFER_UAV_FLAG_APPEND = 0x00000002,
    D3D11_BUFFER_UAV_FLAG_COUNTER = 0x00000004,
} D3D11_BUFFER_UAV_FLAG;

typedef struct D3D11_BUFFER_UAV
{
    UINT FirstElement;
    UINT NumElements;
    UINT Flags; // See D3D11_BUFFER_UAV_FLAG_* below
} D3D11_BUFFER_UAV;

typedef struct D3D11_TEX1D_UAV
{
    UINT MipSlice;
} D3D11_TEX1D_UAV;

typedef struct D3D11_TEX1D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX1D_ARRAY_UAV;

typedef struct D3D11_TEX2D_UAV
{
    UINT MipSlice;
} D3D11_TEX2D_UAV;

typedef struct D3D11_TEX2D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D11_TEX2D_ARRAY_UAV;

typedef struct D3D11_TEX3D_UAV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D11_TEX3D_UAV;
 
typedef struct D3D11_UNORDERED_ACCESS_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D11_UAV_DIMENSION ViewDimension;

    union
    {
        D3D11_BUFFER_UAV Buffer;
        D3D11_TEX1D_UAV Texture1D;
        D3D11_TEX1D_ARRAY_UAV Texture1DArray;
        D3D11_TEX2D_UAV Texture2D;
        D3D11_TEX2D_ARRAY_UAV Texture2DArray;
        D3D11_TEX3D_UAV Texture3D;
    };
} D3D11_UNORDERED_ACCESS_VIEW_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_UNORDERED_ACCESS_VIEW_DESC : public D3D11_UNORDERED_ACCESS_VIEW_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_UNORDERED_ACCESS_VIEW_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_UNORDERED_ACCESS_VIEW_DESC( const D3D11_UNORDERED_ACCESS_VIEW_DESC& o ) :" )
cpp_quote( "        D3D11_UNORDERED_ACCESS_VIEW_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_UNORDERED_ACCESS_VIEW_DESC(" )
cpp_quote( "        D3D11_UAV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0, // FirstElement for BUFFER" )
cpp_quote( "        UINT firstArraySlice = 0, // NumElements for BUFFER, FirstWSlice for TEXTURE3D" )
cpp_quote( "        UINT arraySize = -1, // WSize for TEXTURE3D" )
cpp_quote( "        UINT flags = 0 ) // BUFFER only" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_UAV_DIMENSION_BUFFER:" )
cpp_quote( "            Buffer.FirstElement = mipSlice;" )
cpp_quote( "            Buffer.NumElements = firstArraySlice;" )
cpp_quote( "            Buffer.Flags = flags;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE3D:" )
cpp_quote( "            Texture3D.MipSlice = mipSlice;" )
cpp_quote( "            Texture3D.FirstWSlice = firstArraySlice;" )
cpp_quote( "            Texture3D.WSize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_UNORDERED_ACCESS_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Buffer*," )
cpp_quote( "        DXGI_FORMAT format," )
cpp_quote( "        UINT firstElement," )
cpp_quote( "        UINT numElements," )
cpp_quote( "        UINT flags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Format = format;" )
cpp_quote( "        ViewDimension = D3D11_UAV_DIMENSION_BUFFER;" )
cpp_quote( "        Buffer.FirstElement = firstElement;" )
cpp_quote( "        Buffer.NumElements = numElements;" )
cpp_quote( "        Buffer.Flags = flags;" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_UNORDERED_ACCESS_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture1D* pTex1D," )
cpp_quote( "        D3D11_UAV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format ||" )
cpp_quote( "            (-1 == arraySize && D3D11_UAV_DIMENSION_TEXTURE1DARRAY == viewDimension))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE1D_DESC TexDesc;" )
cpp_quote( "            pTex1D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE1D:" )
cpp_quote( "            Texture1D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE1DARRAY:" )
cpp_quote( "            Texture1DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture1DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture1DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_UNORDERED_ACCESS_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture2D* pTex2D," )
cpp_quote( "        D3D11_UAV_DIMENSION viewDimension," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstArraySlice = 0," )
cpp_quote( "        UINT arraySize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = viewDimension;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || " )
cpp_quote( "            (-1 == arraySize && D3D11_UAV_DIMENSION_TEXTURE2DARRAY == viewDimension))" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE2D_DESC TexDesc;" )
cpp_quote( "            pTex2D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == arraySize) arraySize = TexDesc.ArraySize - firstArraySlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        switch (viewDimension)" )
cpp_quote( "        {" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE2D:" )
cpp_quote( "            Texture2D.MipSlice = mipSlice;" )
cpp_quote( "            break;" )
cpp_quote( "        case D3D11_UAV_DIMENSION_TEXTURE2DARRAY:" )
cpp_quote( "            Texture2DArray.MipSlice = mipSlice;" )
cpp_quote( "            Texture2DArray.FirstArraySlice = firstArraySlice;" )
cpp_quote( "            Texture2DArray.ArraySize = arraySize;" )
cpp_quote( "            break;" )
cpp_quote( "        default: break;" )
cpp_quote( "        }" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_UNORDERED_ACCESS_VIEW_DESC(" )
cpp_quote( "        __in ID3D11Texture3D* pTex3D," )
cpp_quote( "        DXGI_FORMAT format = DXGI_FORMAT_UNKNOWN," )
cpp_quote( "        UINT mipSlice = 0," )
cpp_quote( "        UINT firstWSlice = 0," )
cpp_quote( "        UINT wSize = -1 )" )
cpp_quote( "    {" )
cpp_quote( "        ViewDimension = D3D11_UAV_DIMENSION_TEXTURE3D;" )
cpp_quote( "        if (DXGI_FORMAT_UNKNOWN == format || -1 == wSize)" )
cpp_quote( "        {" )
cpp_quote( "            D3D11_TEXTURE3D_DESC TexDesc;" )
cpp_quote( "            pTex3D->GetDesc( &TexDesc );" )
cpp_quote( "            if (DXGI_FORMAT_UNKNOWN == format) format = TexDesc.Format;" )
cpp_quote( "            if (-1 == wSize) wSize = TexDesc.Depth - firstWSlice;" )
cpp_quote( "        }" )
cpp_quote( "        Format = format;" )
cpp_quote( "        Texture3D.MipSlice = mipSlice;" )
cpp_quote( "        Texture3D.FirstWSlice = firstWSlice;" )
cpp_quote( "        Texture3D.WSize = wSize;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_UNORDERED_ACCESS_VIEW_DESC() {}" )
cpp_quote( "    operator const D3D11_UNORDERED_ACCESS_VIEW_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( 28acf509-7f5c-48f6-8611-f316010a6380 ), object, local, pointer_default( unique ) ]
interface ID3D11UnorderedAccessView
    : ID3D11View
{
    void GetDesc( [annotation("__out")] D3D11_UNORDERED_ACCESS_VIEW_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Vertex Shader
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( 3b301d64-d678-4289-8897-22f8928b72f3 ), object, local, pointer_default( unique ) ]
interface ID3D11VertexShader
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Hull Shader
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( 8e5c6061-628a-4c8e-8264-bbe45cb3d5dd ), object, local, pointer_default( unique ) ]
interface ID3D11HullShader
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Domain Shader
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( f582c508-0f36-490c-9977-31eece268cfa ), object, local, pointer_default( unique ) ]
interface ID3D11DomainShader
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Geometry Shader
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( 38325b96-effb-4022-ba02-2e795b70275c ), object, local, pointer_default( unique ) ]
interface ID3D11GeometryShader
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Pixel Shader
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( ea82e40d-51dc-4f33-93d4-db7c9125ae8c ), object, local, pointer_default( unique ) ]
interface ID3D11PixelShader
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Compute Shader
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( 4f5b196e-c2bd-495e-bd01-1fded38e4969 ), object, local, pointer_default( unique ) ]
interface ID3D11ComputeShader
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// InputLayout
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( e4819ddc-4cf0-4025-bd26-5de82a3e07b7 ), object, local, pointer_default( unique ) ]
interface ID3D11InputLayout
    : ID3D11DeviceChild
{
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Sampler
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef enum D3D11_FILTER
{
    // Bits used in defining enumeration of valid filters:
    // bits [1:0] - mip: 0 == point, 1 == linear, 2,3 unused
    // bits [3:2] - mag: 0 == point, 1 == linear, 2,3 unused
    // bits [5:4] - min: 0 == point, 1 == linear, 2,3 unused
    // bit  [6]   - aniso
    // bit  [7]   - comparison
    // bit  [31]  - mono 1-bit (narrow-purpose filter)

    D3D11_FILTER_MIN_MAG_MIP_POINT                              = 0x00000000,
    D3D11_FILTER_MIN_MAG_POINT_MIP_LINEAR                       = 0x00000001,
    D3D11_FILTER_MIN_POINT_MAG_LINEAR_MIP_POINT                 = 0x00000004,
    D3D11_FILTER_MIN_POINT_MAG_MIP_LINEAR                       = 0x00000005,
    D3D11_FILTER_MIN_LINEAR_MAG_MIP_POINT                       = 0x00000010,
    D3D11_FILTER_MIN_LINEAR_MAG_POINT_MIP_LINEAR                = 0x00000011,
    D3D11_FILTER_MIN_MAG_LINEAR_MIP_POINT                       = 0x00000014,
    D3D11_FILTER_MIN_MAG_MIP_LINEAR                             = 0x00000015,
    D3D11_FILTER_ANISOTROPIC                                    = 0x00000055,
    D3D11_FILTER_COMPARISON_MIN_MAG_MIP_POINT                   = 0x00000080,
    D3D11_FILTER_COMPARISON_MIN_MAG_POINT_MIP_LINEAR            = 0x00000081,
    D3D11_FILTER_COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT      = 0x00000084,
    D3D11_FILTER_COMPARISON_MIN_POINT_MAG_MIP_LINEAR            = 0x00000085,
    D3D11_FILTER_COMPARISON_MIN_LINEAR_MAG_MIP_POINT            = 0x00000090,
    D3D11_FILTER_COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR     = 0x00000091,
    D3D11_FILTER_COMPARISON_MIN_MAG_LINEAR_MIP_POINT            = 0x00000094,
    D3D11_FILTER_COMPARISON_MIN_MAG_MIP_LINEAR                  = 0x00000095,
    D3D11_FILTER_COMPARISON_ANISOTROPIC                         = 0x000000d5,
} D3D11_FILTER;

typedef enum D3D11_FILTER_TYPE
{
    D3D11_FILTER_TYPE_POINT = 0,
    D3D11_FILTER_TYPE_LINEAR = 1,
} D3D11_FILTER_TYPE;

const UINT D3D11_FILTER_TYPE_MASK = 0x00000003;
const UINT D3D11_MIN_FILTER_SHIFT = 4;
const UINT D3D11_MAG_FILTER_SHIFT = 2;
const UINT D3D11_MIP_FILTER_SHIFT = 0;
const UINT D3D11_COMPARISON_FILTERING_BIT = 0x00000080;
const UINT D3D11_ANISOTROPIC_FILTERING_BIT = 0x00000040;

// encode enum entry for most filters except anisotropic filtering
cpp_quote( "#define D3D11_ENCODE_BASIC_FILTER( min, mag, mip, bComparison )                                           \\" )
cpp_quote( "                                   ( ( D3D11_FILTER ) (                                                   \\" )
cpp_quote( "                                   ( ( bComparison ) ? D3D11_COMPARISON_FILTERING_BIT : 0 ) |             \\" )
cpp_quote( "                                   ( ( ( min ) & D3D11_FILTER_TYPE_MASK ) << D3D11_MIN_FILTER_SHIFT ) |   \\" )
cpp_quote( "                                   ( ( ( mag ) & D3D11_FILTER_TYPE_MASK ) << D3D11_MAG_FILTER_SHIFT ) |   \\" )
cpp_quote( "                                   ( ( ( mip ) & D3D11_FILTER_TYPE_MASK ) << D3D11_MIP_FILTER_SHIFT ) ) )   " )

// encode enum entry for anisotropic filtering (with or without comparison filtering)
cpp_quote( "#define D3D11_ENCODE_ANISOTROPIC_FILTER( bComparison )                                                \\" )
cpp_quote( "                                         ( ( D3D11_FILTER ) (                                         \\" )
cpp_quote( "                                         D3D11_ANISOTROPIC_FILTERING_BIT |                            \\" )
cpp_quote( "                                         D3D11_ENCODE_BASIC_FILTER( D3D11_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    D3D11_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    D3D11_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    bComparison ) ) )                   " )

cpp_quote( "#define D3D11_DECODE_MIN_FILTER( d3d11Filter )                                                              \\" )
cpp_quote( "                                 ( ( D3D11_FILTER_TYPE )                                                    \\" )
cpp_quote( "                                 ( ( ( d3d11Filter ) >> D3D11_MIN_FILTER_SHIFT ) & D3D11_FILTER_TYPE_MASK ) ) " )

cpp_quote( "#define D3D11_DECODE_MAG_FILTER( d3d11Filter )                                                              \\" )
cpp_quote( "                                 ( ( D3D11_FILTER_TYPE )                                                    \\" )
cpp_quote( "                                 ( ( ( d3d11Filter ) >> D3D11_MAG_FILTER_SHIFT ) & D3D11_FILTER_TYPE_MASK ) ) " )

cpp_quote( "#define D3D11_DECODE_MIP_FILTER( d3d11Filter )                                                              \\" )
cpp_quote( "                                 ( ( D3D11_FILTER_TYPE )                                                    \\" )
cpp_quote( "                                 ( ( ( d3d11Filter ) >> D3D11_MIP_FILTER_SHIFT ) & D3D11_FILTER_TYPE_MASK ) ) " )

cpp_quote( "#define D3D11_DECODE_IS_COMPARISON_FILTER( d3d11Filter )                                                    \\" )
cpp_quote( "                                 ( ( d3d11Filter ) & D3D11_COMPARISON_FILTERING_BIT )                         " )

cpp_quote( "#define D3D11_DECODE_IS_ANISOTROPIC_FILTER( d3d11Filter )                                               \\" )
cpp_quote( "                            ( ( ( d3d11Filter ) & D3D11_ANISOTROPIC_FILTERING_BIT ) &&                  \\" )
cpp_quote( "                            ( D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MIN_FILTER( d3d11Filter ) ) &&   \\" )
cpp_quote( "                            ( D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MAG_FILTER( d3d11Filter ) ) &&   \\" )
cpp_quote( "                            ( D3D11_FILTER_TYPE_LINEAR == D3D11_DECODE_MIP_FILTER( d3d11Filter ) ) )      " )

typedef enum D3D11_TEXTURE_ADDRESS_MODE
{
    D3D11_TEXTURE_ADDRESS_WRAP = 1,
    D3D11_TEXTURE_ADDRESS_MIRROR = 2,
    D3D11_TEXTURE_ADDRESS_CLAMP = 3,
    D3D11_TEXTURE_ADDRESS_BORDER = 4,
    D3D11_TEXTURE_ADDRESS_MIRROR_ONCE = 5
} D3D11_TEXTURE_ADDRESS_MODE;

typedef struct D3D11_SAMPLER_DESC
{
    D3D11_FILTER Filter;
    D3D11_TEXTURE_ADDRESS_MODE AddressU;
    D3D11_TEXTURE_ADDRESS_MODE AddressV;
    D3D11_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D11_COMPARISON_FUNC ComparisonFunc;
    FLOAT BorderColor[4]; // RGBA
    FLOAT MinLOD;
    FLOAT MaxLOD;
} D3D11_SAMPLER_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_SAMPLER_DESC : public D3D11_SAMPLER_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_SAMPLER_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_SAMPLER_DESC( const D3D11_SAMPLER_DESC& o ) :" )
cpp_quote( "        D3D11_SAMPLER_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_SAMPLER_DESC( CD3D11_DEFAULT )" )
cpp_quote( "    {" )
cpp_quote( "        Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;" )
cpp_quote( "        AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;" )
cpp_quote( "        AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;" )
cpp_quote( "        AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;" )
cpp_quote( "        MipLODBias = 0;" )
cpp_quote( "        MaxAnisotropy = 1;" )
cpp_quote( "        ComparisonFunc = D3D11_COMPARISON_NEVER;" )
cpp_quote( "        BorderColor[ 0 ] = 1.0f;" )
cpp_quote( "        BorderColor[ 1 ] = 1.0f;" )
cpp_quote( "        BorderColor[ 2 ] = 1.0f;" )
cpp_quote( "        BorderColor[ 3 ] = 1.0f;" )
cpp_quote( "        MinLOD = -3.402823466e+38F; // -FLT_MAX" )
cpp_quote( "        MaxLOD = 3.402823466e+38F; // FLT_MAX" )
cpp_quote( "    }" )
cpp_quote( "    explicit CD3D11_SAMPLER_DESC(" )
cpp_quote( "        D3D11_FILTER filter," )
cpp_quote( "        D3D11_TEXTURE_ADDRESS_MODE addressU," )
cpp_quote( "        D3D11_TEXTURE_ADDRESS_MODE addressV," )
cpp_quote( "        D3D11_TEXTURE_ADDRESS_MODE addressW," )
cpp_quote( "        FLOAT mipLODBias," )
cpp_quote( "        UINT maxAnisotropy," )
cpp_quote( "        D3D11_COMPARISON_FUNC comparisonFunc," )
cpp_quote( "        __in_ecount_opt( 4 ) const FLOAT* borderColor, // RGBA" )
cpp_quote( "        FLOAT minLOD," )
cpp_quote( "        FLOAT maxLOD )" )
cpp_quote( "    {" )
cpp_quote( "        Filter = filter;" )
cpp_quote( "        AddressU = addressU;" )
cpp_quote( "        AddressV = addressV;" )
cpp_quote( "        AddressW = addressW;" )
cpp_quote( "        MipLODBias = mipLODBias;" )
cpp_quote( "        MaxAnisotropy = maxAnisotropy;" )
cpp_quote( "        ComparisonFunc = comparisonFunc;" )
cpp_quote( "        const float defaultColor[ 4 ] = { 1.0f, 1.0f, 1.0f, 1.0f };" )
cpp_quote( "        if (!borderColor) borderColor = defaultColor;" )
cpp_quote( "        BorderColor[ 0 ] = borderColor[ 0 ];" )
cpp_quote( "        BorderColor[ 1 ] = borderColor[ 1 ];" )
cpp_quote( "        BorderColor[ 2 ] = borderColor[ 2 ];" )
cpp_quote( "        BorderColor[ 3 ] = borderColor[ 3 ];" )
cpp_quote( "        MinLOD = minLOD;" )
cpp_quote( "        MaxLOD = maxLOD;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_SAMPLER_DESC() {}" )
cpp_quote( "    operator const D3D11_SAMPLER_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( da6fea51-564c-4487-9810-f0d0f9b4e3a5 ), object, local, pointer_default( unique ) ]
interface ID3D11SamplerState
    : ID3D11DeviceChild
{
    void GetDesc( [annotation("__out")] D3D11_SAMPLER_DESC* pDesc );
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Format Support Flags for CheckFormatSupport API
// Extended enum (and the original one) are also used in CheckFeatureSupport API (which is a superset of CheckFormatSupport).
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

typedef enum D3D11_FORMAT_SUPPORT
{
    D3D11_FORMAT_SUPPORT_BUFFER = 0x00000001,
    D3D11_FORMAT_SUPPORT_IA_VERTEX_BUFFER = 0x00000002,
    D3D11_FORMAT_SUPPORT_IA_INDEX_BUFFER = 0x00000004,
    D3D11_FORMAT_SUPPORT_SO_BUFFER = 0x00000008,
    D3D11_FORMAT_SUPPORT_TEXTURE1D = 0x00000010,
    D3D11_FORMAT_SUPPORT_TEXTURE2D = 0x00000020,
    D3D11_FORMAT_SUPPORT_TEXTURE3D = 0x00000040,
    D3D11_FORMAT_SUPPORT_TEXTURECUBE = 0x00000080,
    D3D11_FORMAT_SUPPORT_SHADER_LOAD = 0x00000100,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE = 0x00000200,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE_COMPARISON = 0x00000400,
    D3D11_FORMAT_SUPPORT_SHADER_SAMPLE_MONO_TEXT = 0x00000800,
    D3D11_FORMAT_SUPPORT_MIP = 0x00001000,
    D3D11_FORMAT_SUPPORT_MIP_AUTOGEN = 0x00002000,
    D3D11_FORMAT_SUPPORT_RENDER_TARGET = 0x00004000,
    D3D11_FORMAT_SUPPORT_BLENDABLE = 0x00008000,
    D3D11_FORMAT_SUPPORT_DEPTH_STENCIL = 0x00010000,
    D3D11_FORMAT_SUPPORT_CPU_LOCKABLE = 0x00020000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_RESOLVE = 0x00040000,
    D3D11_FORMAT_SUPPORT_DISPLAY = 0x00080000,
    D3D11_FORMAT_SUPPORT_CAST_WITHIN_BIT_LAYOUT = 0x00100000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_RENDERTARGET = 0x00200000,
    D3D11_FORMAT_SUPPORT_MULTISAMPLE_LOAD = 0x00400000,
    D3D11_FORMAT_SUPPORT_SHADER_GATHER = 0x00800000,
    D3D11_FORMAT_SUPPORT_BACK_BUFFER_CAST = 0x01000000,
    D3D11_FORMAT_SUPPORT_TYPED_UNORDERED_ACCESS_VIEW = 0x02000000,
    D3D11_FORMAT_SUPPORT_SHADER_GATHER_COMPARISON = 0x04000000,
} D3D11_FORMAT_SUPPORT;

// D3D11_FORMAT_SUPPORT2 is only used in CheckFeatureSupport
typedef enum D3D11_FORMAT_SUPPORT2
{
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_ADD                                    = 0x00000001,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS                            = 0x00000002,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE      = 0x00000004,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE                               = 0x00000008,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX                      = 0x00000010,
    D3D11_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX                    = 0x00000020,
    D3D11_FORMAT_SUPPORT2_UAV_TYPED_LOAD                                    = 0x00000040,
    D3D11_FORMAT_SUPPORT2_UAV_TYPED_STORE                                   = 0x00000080,
} D3D11_FORMAT_SUPPORT2;


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Query
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
[ uuid( 4b35d0cd-1e15-4258-9c98-1b1333f6dd3b ), object, local, pointer_default( unique ) ]
interface ID3D11Asynchronous
    : ID3D11DeviceChild
{
    UINT GetDataSize();
};

typedef enum D3D11_ASYNC_GETDATA_FLAG
{
        D3D11_ASYNC_GETDATA_DONOTFLUSH = 0x1,
} D3D11_ASYNC_GETDATA_FLAG;


typedef enum D3D11_QUERY
{
    D3D11_QUERY_EVENT = 0,
    D3D11_QUERY_OCCLUSION,
    D3D11_QUERY_TIMESTAMP,
    D3D11_QUERY_TIMESTAMP_DISJOINT,
    D3D11_QUERY_PIPELINE_STATISTICS,
    D3D11_QUERY_OCCLUSION_PREDICATE,
    D3D11_QUERY_SO_STATISTICS,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE,
    D3D11_QUERY_SO_STATISTICS_STREAM0,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM0,
    D3D11_QUERY_SO_STATISTICS_STREAM1,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM1,
    D3D11_QUERY_SO_STATISTICS_STREAM2,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM2,
    D3D11_QUERY_SO_STATISTICS_STREAM3,
    D3D11_QUERY_SO_OVERFLOW_PREDICATE_STREAM3,
} D3D11_QUERY;

typedef enum D3D11_QUERY_MISC_FLAG
{
        D3D11_QUERY_MISC_PREDICATEHINT = 0x1,
} D3D11_QUERY_MISC_FLAG;

typedef struct D3D11_QUERY_DESC
{
    D3D11_QUERY Query;
    UINT MiscFlags;
} D3D11_QUERY_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_QUERY_DESC : public D3D11_QUERY_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_QUERY_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_QUERY_DESC( const D3D11_QUERY_DESC& o ) :" )
cpp_quote( "        D3D11_QUERY_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_QUERY_DESC(" )
cpp_quote( "        D3D11_QUERY query," )
cpp_quote( "        UINT miscFlags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Query = query;" )
cpp_quote( "        MiscFlags = miscFlags;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_QUERY_DESC() {}" )
cpp_quote( "    operator const D3D11_QUERY_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )

[ uuid( d6c00747-87b7-425e-b84d-44d108560afd ), object, local, pointer_default( unique ) ]
interface ID3D11Query
    : ID3D11Asynchronous
{
    void GetDesc( [annotation("__out")] D3D11_QUERY_DESC* pDesc );
};

[ uuid( 9eb576dd-9f77-4d86-81aa-8bab5fe490e2 ), object, local, pointer_default( unique ) ]
interface ID3D11Predicate
    : ID3D11Query
{
};

typedef struct D3D11_QUERY_DATA_TIMESTAMP_DISJOINT
{
    UINT64 Frequency;
    BOOL Disjoint;
} D3D11_QUERY_DATA_TIMESTAMP_DISJOINT;

typedef struct D3D11_QUERY_DATA_PIPELINE_STATISTICS
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
} D3D11_QUERY_DATA_PIPELINE_STATISTICS;

typedef struct D3D11_QUERY_DATA_SO_STATISTICS
{
    UINT64 NumPrimitivesWritten;
    UINT64 PrimitivesStorageNeeded;
} D3D11_QUERY_DATA_SO_STATISTICS;

typedef enum D3D11_COUNTER
{
    D3D11_COUNTER_DEVICE_DEPENDENT_0 = 0x40000000, // DO NOT define any more D3D11_COUNTER values after this.
} D3D11_COUNTER;

typedef enum D3D11_COUNTER_TYPE
{
    D3D11_COUNTER_TYPE_FLOAT32,
    D3D11_COUNTER_TYPE_UINT16,
    D3D11_COUNTER_TYPE_UINT32,
    D3D11_COUNTER_TYPE_UINT64,
} D3D11_COUNTER_TYPE;

typedef struct D3D11_COUNTER_DESC
{
    D3D11_COUNTER Counter;
    UINT MiscFlags;
} D3D11_COUNTER_DESC;

cpp_quote( "#if !defined( D3D11_NO_HELPERS ) && defined( __cplusplus )" )
cpp_quote( "}" ) // extern C
cpp_quote( "struct CD3D11_COUNTER_DESC : public D3D11_COUNTER_DESC" )
cpp_quote( "{" )
cpp_quote( "    CD3D11_COUNTER_DESC()" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_COUNTER_DESC( const D3D11_COUNTER_DESC& o ) :" )
cpp_quote( "        D3D11_COUNTER_DESC( o )" )
cpp_quote( "    {}" )
cpp_quote( "    explicit CD3D11_COUNTER_DESC(" )
cpp_quote( "        D3D11_COUNTER counter," )
cpp_quote( "        UINT miscFlags = 0 )" )
cpp_quote( "    {" )
cpp_quote( "        Counter = counter;" )
cpp_quote( "        MiscFlags = miscFlags;" )
cpp_quote( "    }" )
cpp_quote( "    ~CD3D11_COUNTER_DESC() {}" )
cpp_quote( "    operator const D3D11_COUNTER_DESC&() const { return *this; }" )
cpp_quote( "};" )
cpp_quote( "extern \"C\"{" )
cpp_quote( "#endif" )


typedef struct D3D11_COUNTER_INFO
{
    D3D11_COUNTER LastDeviceDependentCounter;
    UINT NumSimultaneousCounters;
    UINT8 NumDetectableParallelUnits;
} D3D11_COUNTER_INFO;

[ uuid( 6e8c49fb-a371-4770-b440-29086022b741 ), object, local, pointer_default( unique ) ]
interface ID3D11Counter
    : ID3D11Asynchronous
{
    void GetDesc( [annotation("__out")] D3D11_COUNTER_DESC* pDesc );
};

typedef enum D3D11_STANDARD_MULTISAMPLE_QUALITY_LEVELS
{
    D3D11_STANDARD_MULTISAMPLE_PATTERN = 0xffffffff,
    D3D11_CENTER_MULTISAMPLE_PATTERN = 0xfffffffe
} D3D11_STANDARD_MULTISAMPLE_QUALITY_LEVELS;

typedef enum D3D11_DEVICE_CONTEXT_TYPE
{
    D3D11_DEVICE_CONTEXT_IMMEDIATE,
    D3D11_DEVICE_CONTEXT_DEFERRED,
} D3D11_DEVICE_CONTEXT_TYPE;

typedef struct D3D11_CLASS_INSTANCE_DESC
{
    // the first two entries are valid if the instance was created using GetClassInstance
    UINT InstanceId;
    UINT InstanceIndex; // entry in an array of instances
    // the rest of the entries are valid for instances created with CreateClassInstance
    UINT TypeId;
    UINT ConstantBuffer;
    UINT BaseConstantBufferOffset;
    UINT BaseTexture;
    UINT BaseSampler;
    // this lets you know which of the two an instance is
    BOOL Created;
} D3D11_CLASS_INSTANCE_DESC;

[ uuid( a6cd7faa-b0b7-4a2f-9436-8662a65797cb ), object, local, pointer_default( unique ) ]
interface ID3D11ClassInstance
    : ID3D11DeviceChild
{
    void GetClassLinkage(
        [annotation("__out")] ID3D11ClassLinkage** ppLinkage);
    void GetDesc(
        [annotation("__out")] D3D11_CLASS_INSTANCE_DESC* pDesc);
    void GetInstanceName(
        [annotation("__out_ecount_opt(*pBufferLength)")] LPSTR pInstanceName,
        [annotation("__inout")] SIZE_T* pBufferLength);
    void GetTypeName(
        [annotation("__out_ecount_opt(*pBufferLength)")] LPSTR pTypeName,
        [annotation("__inout")] SIZE_T* pBufferLength);
};

[ uuid( ddf57cba-9543-46e4-a12b-f207a0fe7fed ), object, local, pointer_default( unique ) ]
interface ID3D11ClassLinkage
    : ID3D11DeviceChild
{
    // Get a reference to an instance of a class
    // that exists in a shader.  The common scenario is to refer to
    // variables declared in shaders, which means that a reference is
    // acquired with this function and then passed in on SetShader.
    HRESULT GetClassInstance(
        [annotation("__in")] LPCSTR pClassInstanceName,
        [annotation("__in")] UINT InstanceIndex,
        [annotation("__out")] ID3D11ClassInstance** ppInstance);

    // Create a class instance reference that is the combination of a class
    // type and the location of the data to use for the class instance
    // - not the common scenario, but useful in case the data location
    // for a class is dynamic or not known until runtime.
    HRESULT CreateClassInstance(
        [annotation("__in")] LPCSTR pClassTypeName,
        [annotation("__in")] UINT ConstantBufferOffset,
        [annotation("__in")] UINT ConstantVectorOffset,
        [annotation("__in")] UINT TextureOffset,
        [annotation("__in")] UINT SamplerOffset,
        [annotation("__out")] ID3D11ClassInstance** ppInstance);
};

[ uuid( a24bc4d1-769e-43f7-8013-98ff566c18e2 ), object, local, pointer_default( unique ) ]
interface ID3D11CommandList
    : ID3D11DeviceChild
{
    UINT GetContextFlags();
};

typedef enum D3D11_FEATURE
{
    D3D11_FEATURE_THREADING, /* D3D11_FEATURE_DATA_THREADING */
    D3D11_FEATURE_DOUBLES,  /* D3D11_FEATURE_DATA_DOUBLES */
    D3D11_FEATURE_FORMAT_SUPPORT, /* D3D11_FEATURE_DATA_FORMAT_SUPPORT */
    D3D11_FEATURE_FORMAT_SUPPORT2, /* D3D11_FEATURE_DATA_FORMAT_SUPPORT2 */
    D3D11_FEATURE_D3D10_X_HARDWARE_OPTIONS, /* D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS */
} D3D11_FEATURE;

typedef struct D3D11_FEATURE_DATA_THREADING
{
    BOOL DriverConcurrentCreates;
    BOOL DriverCommandLists;
} D3D11_FEATURE_DATA_THREADING;

typedef struct D3D11_FEATURE_DATA_DOUBLES
{
    BOOL DoublePrecisionFloatShaderOps;
} D3D11_FEATURE_DATA_DOUBLES;

typedef struct D3D11_FEATURE_DATA_FORMAT_SUPPORT
{
    DXGI_FORMAT InFormat;
    UINT OutFormatSupport;
} D3D11_FEATURE_DATA_FORMAT_SUPPORT;

typedef struct D3D11_FEATURE_DATA_FORMAT_SUPPORT2
{
    DXGI_FORMAT InFormat;
    UINT OutFormatSupport2;
} D3D11_FEATURE_DATA_FORMAT_SUPPORT2;

typedef struct D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS
{
    BOOL ComputeShaders_Plus_RawAndStructuredBuffers_Via_Shader_4_x;
} D3D11_FEATURE_DATA_D3D10_X_HARDWARE_OPTIONS;
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// DeviceContext
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

[ uuid( c0bfa96c-e089-44fb-8eaf-26f8796190da ), object, local, pointer_default( unique ) ]
interface ID3D11DeviceContext
    : ID3D11DeviceChild
{
    // !!! Order of functions is in decreasing order of priority ( as far as performance is concerned ) !!!
    // !!! BEGIN HIGH-FREQUENCY !!!
    void VSSetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppConstantBuffers );
    void PSSetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__in_ecount(NumViews)")] ID3D11ShaderResourceView*const* ppShaderResourceViews );
    void PSSetShader(
        [annotation("__in_opt")] ID3D11PixelShader* pPixelShader,
        [annotation("__in_ecount_opt(NumClassInstances)")] ID3D11ClassInstance*const* ppClassInstances,
        UINT NumClassInstances );
    void PSSetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__in_ecount(NumSamplers)")] ID3D11SamplerState*const* ppSamplers );
    void VSSetShader(
        [annotation("__in_opt")] ID3D11VertexShader* pVertexShader,
        [annotation("__in_ecount_opt(NumClassInstances)")] ID3D11ClassInstance*const* ppClassInstances,
        UINT NumClassInstances );
    void DrawIndexed(
        [annotation("__in")] UINT IndexCount,
        [annotation("__in")] UINT StartIndexLocation,
        [annotation("__in")] INT BaseVertexLocation );
    void Draw(
        [annotation("__in")] UINT VertexCount,
        [annotation("__in")] UINT StartVertexLocation );
    HRESULT Map(
        [annotation("__in")] ID3D11Resource* pResource,
        [annotation("__in")] UINT Subresource,
        [annotation("__in")] D3D11_MAP MapType,
        [annotation("__in")] UINT MapFlags,
        [annotation("__out")] D3D11_MAPPED_SUBRESOURCE* pMappedResource );
    void Unmap(
        [annotation("__in")] ID3D11Resource* pResource,
        [annotation("__in")] UINT Subresource );
    void PSSetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppConstantBuffers );
    void IASetInputLayout( [annotation("__in_opt")] ID3D11InputLayout* pInputLayout );
    void IASetVertexBuffers(
        [annotation("__in_range( 0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppVertexBuffers,
        [annotation("__in_ecount(NumBuffers)")] const UINT* pStrides,
        [annotation("__in_ecount(NumBuffers)")] const UINT* pOffsets );
    void IASetIndexBuffer(
        [annotation("__in_opt")] ID3D11Buffer* pIndexBuffer,
        [annotation("__in")] DXGI_FORMAT  Format,
        [annotation("__in")] UINT Offset );
    // !!! END HIGH-FREQUENCY !!!

    // !!! Order of functions is in decreasing order of priority ( as far as performance is concerned ) !!!
    // !!! BEGIN MIDDLE-FREQUENCY !!!
    void DrawIndexedInstanced(
        [annotation("__in")] UINT IndexCountPerInstance,
        [annotation("__in")] UINT InstanceCount,
        [annotation("__in")] UINT StartIndexLocation,
        [annotation("__in")] INT BaseVertexLocation,
        [annotation("__in")] UINT StartInstanceLocation );
    void DrawInstanced(
        [annotation("__in")] UINT VertexCountPerInstance,
        [annotation("__in")] UINT InstanceCount,
        [annotation("__in")] UINT StartVertexLocation,
        [annotation("__in")] UINT StartInstanceLocation );
    void GSSetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppConstantBuffers );
    void GSSetShader(
        [annotation("__in_opt")] ID3D11GeometryShader* pShader,
        [annotation("__in_ecount_opt(NumClassInstances)")] ID3D11ClassInstance*const* ppClassInstances,
        UINT NumClassInstances );
    void IASetPrimitiveTopology( [annotation("__in")] D3D11_PRIMITIVE_TOPOLOGY Topology );
    void VSSetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__in_ecount(NumViews)")] ID3D11ShaderResourceView*const* ppShaderResourceViews );
    void VSSetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__in_ecount(NumSamplers)")] ID3D11SamplerState*const* ppSamplers );
    void Begin( [annotation("__in")] ID3D11Asynchronous* pAsync );
    void End( [annotation("__in")] ID3D11Asynchronous* pAsync );
    HRESULT GetData(
        [annotation("__in")] ID3D11Asynchronous* pAsync,
        [annotation("__out_bcount_opt( DataSize )")] void* pData,
        [annotation("__in")] UINT DataSize,
        [annotation("__in")] UINT GetDataFlags );
    void SetPredication(
        [annotation("__in_opt")] ID3D11Predicate* pPredicate,
        [annotation("__in")] BOOL PredicateValue );
    void GSSetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__in_ecount(NumViews)")] ID3D11ShaderResourceView*const* ppShaderResourceViews );
    void GSSetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__in_ecount(NumSamplers)")] ID3D11SamplerState*const* ppSamplers );
    void OMSetRenderTargets(
        [annotation("__in_range( 0, D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT )")] UINT NumViews,
        [annotation("__in_ecount_opt(NumViews)")] ID3D11RenderTargetView*const* ppRenderTargetViews,
        [annotation("__in_opt")] ID3D11DepthStencilView* pDepthStencilView );
    void OMSetRenderTargetsAndUnorderedAccessViews(
        [annotation("__in")] UINT NumRTVs,
        [annotation("__in_ecount_opt(NumRTVs)")] ID3D11RenderTargetView*const* ppRenderTargetViews,
        [annotation("__in_opt")] ID3D11DepthStencilView* pDepthStencilView,
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - 1 )")] UINT UAVStartSlot,
        [annotation("__in")] UINT NumUAVs,
        [annotation("__in_ecount_opt(NumUAVs)")] ID3D11UnorderedAccessView*const* ppUnorderedAccessViews,
        [annotation("__in_ecount_opt(NumUAVs)")] const UINT* pUAVInitialCounts );
    void OMSetBlendState( 
        [annotation("__in_opt")] ID3D11BlendState *pBlendState,
        [annotation("__in_opt")] const FLOAT BlendFactor[ 4 ],
        [annotation("__in")] UINT SampleMask );
    void OMSetDepthStencilState( 
        [annotation("__in_opt")] ID3D11DepthStencilState* pDepthStencilState,
        [annotation("__in")] UINT StencilRef );
    void SOSetTargets(
        [annotation("__in_range( 0, D3D11_SO_BUFFER_SLOT_COUNT)")] UINT NumBuffers,
        [annotation("__in_ecount_opt(NumBuffers)")] ID3D11Buffer*const* ppSOTargets,
        [annotation("__in_ecount_opt(NumBuffers)")] const UINT* pOffsets );
    void DrawAuto();
    void DrawIndexedInstancedIndirect(
        [annotation("__in")] ID3D11Buffer* pBufferForArgs,
        [annotation("__in")] UINT AlignedByteOffsetForArgs );
    void DrawInstancedIndirect(
        [annotation("__in")] ID3D11Buffer* pBufferForArgs,
        [annotation("__in")] UINT AlignedByteOffsetForArgs );
    void Dispatch(
        [annotation("__in")] UINT ThreadGroupCountX,
        [annotation("__in")] UINT ThreadGroupCountY,
        [annotation("__in")] UINT ThreadGroupCountZ );
    void DispatchIndirect(
        [annotation("__in")] ID3D11Buffer* pBufferForArgs,
        [annotation("__in")] UINT AlignedByteOffsetForArgs );
    void RSSetState( [annotation("__in_opt")] ID3D11RasterizerState* pRasterizerState );
    void RSSetViewports(
        [annotation("__in_range(0, D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE)")] UINT NumViewports,
        [annotation("__in_ecount_opt(NumViewports)")] const D3D11_VIEWPORT* pViewports );
    void RSSetScissorRects(
        [annotation("__in_range(0, D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE)")] UINT NumRects,
        [annotation("__in_ecount_opt(NumRects)")] const D3D11_RECT* pRects );
    void CopySubresourceRegion(
        [annotation("__in")] ID3D11Resource* pDstResource,
        [annotation("__in")] UINT DstSubresource,
        [annotation("__in")] UINT DstX,
        [annotation("__in")] UINT DstY,
        [annotation("__in")] UINT DstZ,
        [annotation("__in")] ID3D11Resource* pSrcResource,
        [annotation("__in")] UINT SrcSubresource,
        [annotation("__in_opt")] const D3D11_BOX* pSrcBox );
    void CopyResource(
        [annotation("__in")] ID3D11Resource* pDstResource,
        [annotation("__in")] ID3D11Resource* pSrcResource );
    void UpdateSubresource(
        [annotation("__in")] ID3D11Resource* pDstResource,
        [annotation("__in")] UINT DstSubresource,
        [annotation("__in_opt")] const D3D11_BOX* pDstBox,
        [annotation("__in")] const void* pSrcData,
        [annotation("__in")] UINT SrcRowPitch,
        [annotation("__in")] UINT SrcDepthPitch );
    void CopyStructureCount(
        [annotation("__in")] ID3D11Buffer* pDstBuffer,
        [annotation("__in")] UINT DstAlignedByteOffset,
        [annotation("__in")] ID3D11UnorderedAccessView* pSrcView );
    void ClearRenderTargetView(
        [annotation("__in")] ID3D11RenderTargetView* pRenderTargetView,
        [annotation("__in")] const FLOAT ColorRGBA[ 4 ] );
    void ClearUnorderedAccessViewUint(
        [annotation("__in")] ID3D11UnorderedAccessView* pUnorderedAccessView,
        [annotation("__in")] const UINT Values[4] );
    void ClearUnorderedAccessViewFloat(
        [annotation("__in")] ID3D11UnorderedAccessView* pUnorderedAccessView,
        [annotation("__in")] const FLOAT Values[4] );
    void ClearDepthStencilView(
        [annotation("__in")] ID3D11DepthStencilView* pDepthStencilView,
        [annotation("__in")] UINT ClearFlags,
        [annotation("__in")] FLOAT Depth,
        [annotation("__in")] UINT8 Stencil );
    void GenerateMips( [annotation("__in")] ID3D11ShaderResourceView* pShaderResourceView );
    void SetResourceMinLOD( [annotation("__in")] ID3D11Resource* pResource, FLOAT MinLOD );
    FLOAT GetResourceMinLOD( [annotation("__in")] ID3D11Resource* pResource );
    void ResolveSubresource(
        [annotation("__in")] ID3D11Resource* pDstResource,
        [annotation("__in")] UINT DstSubresource,
        [annotation("__in")] ID3D11Resource* pSrcResource,
        [annotation("__in")] UINT SrcSubresource,
        [annotation("__in")] DXGI_FORMAT Format );
    void ExecuteCommandList(
        [annotation("__in")] ID3D11CommandList* pCommandList,
        BOOL RestoreContextState );
    void HSSetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__in_ecount(NumViews)")] ID3D11ShaderResourceView*const* ppShaderResourceViews );
    void HSSetShader(
        [annotation("__in_opt")] ID3D11HullShader* pHullShader,
        [annotation("__in_ecount_opt(NumClassInstances)")] ID3D11ClassInstance*const* ppClassInstances,
        UINT NumClassInstances );
    void HSSetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__in_ecount(NumSamplers)")] ID3D11SamplerState*const* ppSamplers );
    void HSSetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppConstantBuffers );
    void DSSetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__in_ecount(NumViews)")] ID3D11ShaderResourceView*const* ppShaderResourceViews );
    void DSSetShader(
        [annotation("__in_opt")] ID3D11DomainShader* pDomainShader,
        [annotation("__in_ecount_opt(NumClassInstances)")] ID3D11ClassInstance*const* ppClassInstances,
        UINT NumClassInstances );
    void DSSetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__in_ecount(NumSamplers)")] ID3D11SamplerState*const* ppSamplers );
    void DSSetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppConstantBuffers );
    void CSSetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__in_ecount(NumViews)")] ID3D11ShaderResourceView*const* ppShaderResourceViews );
    void CSSetUnorderedAccessViews(
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - StartSlot )")] UINT NumUAVs,
        [annotation("__in_ecount(NumUAVs)")] ID3D11UnorderedAccessView*const* ppUnorderedAccessViews,
        [annotation("__in_ecount(NumUAVs)")] const UINT* pUAVInitialCounts );
    void CSSetShader(
        [annotation("__in_opt")] ID3D11ComputeShader* pComputeShader,
        [annotation("__in_ecount_opt(NumClassInstances)")] ID3D11ClassInstance*const* ppClassInstances,
        UINT NumClassInstances );
    void CSSetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__in_ecount(NumSamplers)")] ID3D11SamplerState*const* ppSamplers );
    void CSSetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__in_ecount(NumBuffers)")] ID3D11Buffer*const* ppConstantBuffers );

// GET functions
    void VSGetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppConstantBuffers );
    void PSGetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__out_ecount(NumViews)")] ID3D11ShaderResourceView** ppShaderResourceViews );
    void PSGetShader(
        [annotation("__out_opt")] ID3D11PixelShader** ppPixelShader,
        [annotation("__out_ecount_opt(*pNumClassInstances)")] ID3D11ClassInstance** ppClassInstances,
        [annotation("__inout_opt")] UINT* pNumClassInstances);
    void PSGetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__out_ecount(NumSamplers)")] ID3D11SamplerState** ppSamplers );
    void VSGetShader(
        [annotation("__out_opt")] ID3D11VertexShader** ppVertexShader,
        [annotation("__out_ecount_opt(*pNumClassInstances)")] ID3D11ClassInstance** ppClassInstances,
        [annotation("__inout_opt")] UINT* pNumClassInstances);
    void PSGetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppConstantBuffers );
    void IAGetInputLayout( [annotation("__out")] ID3D11InputLayout** ppInputLayout );
    void IAGetVertexBuffers(
        [annotation("__in_range( 0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount_opt(NumBuffers)")] ID3D11Buffer** ppVertexBuffers,
        [annotation("__out_ecount_opt(NumBuffers)")] UINT* pStrides,
        [annotation("__out_ecount_opt(NumBuffers)")] UINT* pOffsets );
    void IAGetIndexBuffer(
        [annotation("__out_opt")] ID3D11Buffer** pIndexBuffer,
        [annotation("__out_opt")] DXGI_FORMAT* Format,
        [annotation("__out_opt")] UINT* Offset );
    void GSGetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppConstantBuffers );
    void GSGetShader(
        [annotation("__out_opt")] ID3D11GeometryShader** ppGeometryShader,
        [annotation("__out_ecount_opt(*pNumClassInstances)")] ID3D11ClassInstance** ppClassInstances,
        [annotation("__inout_opt")] UINT* pNumClassInstances);
    void IAGetPrimitiveTopology( [annotation("__out")] D3D11_PRIMITIVE_TOPOLOGY* pTopology );
    void VSGetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__out_ecount(NumViews)")] ID3D11ShaderResourceView** ppShaderResourceViews );
    void VSGetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__out_ecount(NumSamplers)")] ID3D11SamplerState** ppSamplers );
    void GetPredication(
        [annotation("__out_opt")] ID3D11Predicate** ppPredicate,
        [annotation("__out_opt")] BOOL* pPredicateValue );
    void GSGetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__out_ecount(NumViews)")] ID3D11ShaderResourceView** ppShaderResourceViews );
    void GSGetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__out_ecount(NumSamplers)")] ID3D11SamplerState** ppSamplers );
    void OMGetRenderTargets(
        [annotation("__in_range( 0, D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT )")] UINT NumViews,
        [annotation("__out_ecount_opt(NumViews)")] ID3D11RenderTargetView** ppRenderTargetViews,
        [annotation("__out_opt")] ID3D11DepthStencilView** ppDepthStencilView );
    void OMGetRenderTargetsAndUnorderedAccessViews(
        [annotation("__in_range( 0, D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT )")] UINT NumRTVs,
        [annotation("__out_ecount_opt(NumRTVs)")] ID3D11RenderTargetView** ppRenderTargetViews,
        [annotation("__out_opt")] ID3D11DepthStencilView** ppDepthStencilView,
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - 1 )")] UINT UAVStartSlot,
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - UAVStartSlot )")] UINT NumUAVs,
        [annotation("__out_ecount_opt(NumUAVs)")] ID3D11UnorderedAccessView** ppUnorderedAccessViews );
    void OMGetBlendState( 
        [annotation("__out_opt")] ID3D11BlendState** ppBlendState,
        [annotation("__out_opt")] FLOAT BlendFactor[ 4 ], //4
        [annotation("__out_opt")] UINT* pSampleMask );
    void OMGetDepthStencilState(
        [annotation("__out_opt")] ID3D11DepthStencilState** ppDepthStencilState,
        [annotation("__out_opt")] UINT* pStencilRef );
    void SOGetTargets(
        [annotation("__in_range( 0, D3D11_SO_BUFFER_SLOT_COUNT )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppSOTargets );
    void RSGetState( [annotation("__out")] ID3D11RasterizerState** ppRasterizerState );
    void RSGetViewports(
        [annotation("__inout /*_range(0, D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE )*/ ")] UINT* pNumViewports,
        [annotation("__out_ecount_opt(*pNumViewports)")] D3D11_VIEWPORT* pViewports );
    void RSGetScissorRects(
        [annotation("__inout /*_range(0, D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE )*/ ")] UINT* pNumRects,
        [annotation("__out_ecount_opt(*pNumRects)")] D3D11_RECT* pRects );
    void HSGetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__out_ecount(NumViews)")] ID3D11ShaderResourceView** ppShaderResourceViews );
    void HSGetShader(
        [annotation("__out_opt")] ID3D11HullShader** ppHullShader,
        [annotation("__out_ecount_opt(*pNumClassInstances)")] ID3D11ClassInstance** ppClassInstances,
        [annotation("__inout_opt")] UINT* pNumClassInstances);
    void HSGetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__out_ecount(NumSamplers)")] ID3D11SamplerState** ppSamplers );
    void HSGetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppConstantBuffers );
    void DSGetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__out_ecount(NumViews)")] ID3D11ShaderResourceView** ppShaderResourceViews );
    void DSGetShader(
        [annotation("__out_opt")] ID3D11DomainShader** ppDomainShader,
        [annotation("__out_ecount_opt(*pNumClassInstances)")] ID3D11ClassInstance** ppClassInstances,
        [annotation("__inout_opt")] UINT* pNumClassInstances);
    void DSGetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__out_ecount(NumSamplers)")] ID3D11SamplerState** ppSamplers );
    void DSGetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppConstantBuffers );
    void CSGetShaderResources(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot )")] UINT NumViews,
        [annotation("__out_ecount(NumViews)")] ID3D11ShaderResourceView** ppShaderResourceViews );
    void CSGetUnorderedAccessViews(
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_PS_CS_UAV_REGISTER_COUNT - StartSlot )")] UINT NumUAVs,
        [annotation("__out_ecount(NumUAVs)")] ID3D11UnorderedAccessView** ppUnorderedAccessViews );
    void CSGetShader(
        [annotation("__out_opt")] ID3D11ComputeShader** ppComputeShader,
        [annotation("__out_ecount_opt(*pNumClassInstances)")] ID3D11ClassInstance** ppClassInstances,
        [annotation("__inout_opt")] UINT* pNumClassInstances);
    void CSGetSamplers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot )")] UINT NumSamplers,
        [annotation("__out_ecount(NumSamplers)")] ID3D11SamplerState** ppSamplers );
    void CSGetConstantBuffers(
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1 )")] UINT StartSlot,
        [annotation("__in_range( 0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot )")] UINT NumBuffers,
        [annotation("__out_ecount(NumBuffers)")] ID3D11Buffer** ppConstantBuffers );
// /GET functions

    void ClearState();
    void Flush();
    // !!! END MIDDLE-FREQUENCY !!!

    // !!! Order of functions is in decreasing order of priority ( as far as performance is concerned ) !!!
    // !!! BEGIN LOW-FREQUENCY !!!
    D3D11_DEVICE_CONTEXT_TYPE GetType();
    UINT GetContextFlags();
    HRESULT FinishCommandList(
        BOOL RestoreDeferredContextState,
        [annotation("__out_opt")] ID3D11CommandList** ppCommandList );
    
    // !!! END LOW-FREQUENCY !!!
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Device
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

[ uuid( db6f6ddb-ac77-4e88-8253-819df9bbf140 ), object, local, pointer_default( unique ) ]
interface ID3D11Device
    : IUnknown
{
    // Create*
    HRESULT CreateBuffer(
        [annotation("__in")] const D3D11_BUFFER_DESC* pDesc,
        [annotation("__in_opt")] const D3D11_SUBRESOURCE_DATA* pInitialData,
        [annotation("__out_opt")] ID3D11Buffer** ppBuffer );
    HRESULT CreateTexture1D(
        [annotation("__in")] const D3D11_TEXTURE1D_DESC* pDesc,
        [annotation("__in_xcount_opt(pDesc->MipLevels * pDesc->ArraySize)")] const D3D11_SUBRESOURCE_DATA* pInitialData,
        [annotation("__out_opt")] ID3D11Texture1D** ppTexture1D );
    HRESULT CreateTexture2D(
        [annotation("__in")] const D3D11_TEXTURE2D_DESC* pDesc,
        [annotation("__in_xcount_opt(pDesc->MipLevels * pDesc->ArraySize)")] const D3D11_SUBRESOURCE_DATA* pInitialData,
        [annotation("__out_opt")] ID3D11Texture2D** ppTexture2D );
    HRESULT CreateTexture3D(
        [annotation("__in")] const D3D11_TEXTURE3D_DESC* pDesc,
        [annotation("__in_xcount_opt(pDesc->MipLevels)")] const D3D11_SUBRESOURCE_DATA* pInitialData,
        [annotation("__out_opt")] ID3D11Texture3D** ppTexture3D );
    HRESULT CreateShaderResourceView(
        [annotation("__in")] ID3D11Resource* pResource,
        [annotation("__in_opt")] const D3D11_SHADER_RESOURCE_VIEW_DESC* pDesc,
        [annotation("__out_opt")] ID3D11ShaderResourceView** ppSRView );
    HRESULT CreateUnorderedAccessView(
        [annotation("__in")] ID3D11Resource* pResource,
        [annotation("__in_opt")] const D3D11_UNORDERED_ACCESS_VIEW_DESC* pDesc,
        [annotation("__out_opt")] ID3D11UnorderedAccessView** ppUAView );
    HRESULT CreateRenderTargetView(
        [annotation("__in")] ID3D11Resource* pResource,
        [annotation("__in_opt")] const D3D11_RENDER_TARGET_VIEW_DESC* pDesc,
        [annotation("__out_opt")] ID3D11RenderTargetView** ppRTView );
    HRESULT CreateDepthStencilView(
        [annotation("__in")] ID3D11Resource* pResource,
        [annotation("__in_opt")] const D3D11_DEPTH_STENCIL_VIEW_DESC* pDesc,
        [annotation("__out_opt")] ID3D11DepthStencilView** ppDepthStencilView );
    HRESULT CreateInputLayout(
        [annotation("__in_ecount(NumElements)")] const D3D11_INPUT_ELEMENT_DESC* pInputElementDescs,
        [annotation("__in_range( 0, D3D11_IA_VERTEX_INPUT_STRUCTURE_ELEMENT_COUNT )")] UINT NumElements,
        [annotation("__in")] const void* pShaderBytecodeWithInputSignature,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__out_opt")] ID3D11InputLayout** ppInputLayout );
    HRESULT CreateVertexShader(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11VertexShader** ppVertexShader );
    HRESULT CreateGeometryShader(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11GeometryShader** ppGeometryShader );
    HRESULT CreateGeometryShaderWithStreamOutput(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_ecount_opt(NumEntries)")] const D3D11_SO_DECLARATION_ENTRY* pSODeclaration,
        [annotation("__in_range( 0, D3D11_SO_STREAM_COUNT * D3D11_SO_OUTPUT_COMPONENT_COUNT )")] UINT NumEntries,
        [annotation("__in_ecount_opt(NumStrides)")] const UINT* pBufferStrides,
        [annotation("__in_range( 0, D3D11_SO_BUFFER_SLOT_COUNT )")] UINT NumStrides,
        [annotation("__in")] UINT RasterizedStream,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11GeometryShader** ppGeometryShader );
    HRESULT CreatePixelShader(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11PixelShader** ppPixelShader );
    HRESULT CreateHullShader(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11HullShader** ppHullShader );
    HRESULT CreateDomainShader(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11DomainShader** ppDomainShader );
    HRESULT CreateComputeShader(
        [annotation("__in")] const void* pShaderBytecode,
        [annotation("__in")] SIZE_T BytecodeLength,
        [annotation("__in_opt")] ID3D11ClassLinkage* pClassLinkage,
        [annotation("__out_opt")] ID3D11ComputeShader** ppComputeShader );
    HRESULT CreateClassLinkage(
        [annotation("__out")] ID3D11ClassLinkage** ppLinkage);
    HRESULT CreateBlendState(
        [annotation("__in")] const D3D11_BLEND_DESC* pBlendStateDesc,
        [annotation("__out_opt")] ID3D11BlendState** ppBlendState );
    HRESULT CreateDepthStencilState(
        [annotation("__in")] const D3D11_DEPTH_STENCIL_DESC* pDepthStencilDesc,
        [annotation("__out_opt")] ID3D11DepthStencilState** ppDepthStencilState );
    HRESULT CreateRasterizerState(
        [annotation("__in")] const D3D11_RASTERIZER_DESC* pRasterizerDesc,
        [annotation("__out_opt")] ID3D11RasterizerState** ppRasterizerState );
    HRESULT CreateSamplerState(
        [annotation("__in")] const D3D11_SAMPLER_DESC* pSamplerDesc,
        [annotation("__out_opt")] ID3D11SamplerState** ppSamplerState );
    HRESULT CreateQuery(
        [annotation("__in")] const D3D11_QUERY_DESC* pQueryDesc,
        [annotation("__out_opt")] ID3D11Query** ppQuery );
    HRESULT CreatePredicate(
        [annotation("__in")] const D3D11_QUERY_DESC* pPredicateDesc,
        [annotation("__out_opt")] ID3D11Predicate** ppPredicate );
    HRESULT CreateCounter(
        [annotation("__in")] const D3D11_COUNTER_DESC* pCounterDesc,
        [annotation("__out_opt")] ID3D11Counter** ppCounter );
    HRESULT CreateDeferredContext(
        UINT ContextFlags, // Reserved parameter; must be 0
        [annotation("__out_opt")] ID3D11DeviceContext** ppDeferredContext );
    HRESULT OpenSharedResource(
        [annotation("__in")] HANDLE hResource,
        [annotation("__in")] REFIID ReturnedInterface,
        [annotation("__out_opt")] void ** ppResource);

    // Check*
    HRESULT CheckFormatSupport(
        [annotation("__in")] DXGI_FORMAT Format,
        [annotation("__out")] UINT* pFormatSupport );
    HRESULT CheckMultisampleQualityLevels(
        [annotation("__in")] DXGI_FORMAT Format,
        [annotation("__in")] UINT        SampleCount,
        [annotation("__out")] UINT* pNumQualityLevels );
    void CheckCounterInfo( [annotation("__out")] D3D11_COUNTER_INFO* pCounterInfo );
    HRESULT CheckCounter(
        [annotation("__in")] const D3D11_COUNTER_DESC* pDesc,
        [annotation("__out")] D3D11_COUNTER_TYPE* pType,
        [annotation("__out")] UINT* pActiveCounters,
        [annotation("__out_ecount_opt(*pNameLength)")] LPSTR szName,
        [annotation("__inout_opt")] UINT* pNameLength,
        [annotation("__out_ecount_opt(*pUnitsLength)")] LPSTR szUnits,
        [annotation("__inout_opt")] UINT* pUnitsLength,
        [annotation("__out_ecount_opt(*pDescriptionLength)")] LPSTR szDescription,
        [annotation("__inout_opt")] UINT* pDescriptionLength );
    HRESULT CheckFeatureSupport(
        D3D11_FEATURE Feature,
        [annotation("__out_bcount(FeatureSupportDataSize)")] void* pFeatureSupportData,
        UINT FeatureSupportDataSize );

    HRESULT GetPrivateData(
        [annotation("__in")] REFGUID guid,
        [annotation("__inout")] UINT* pDataSize,
        [annotation("__out_bcount_opt(*pDataSize)")] void* pData );
    HRESULT SetPrivateData(
        [annotation("__in")] REFGUID guid,
        [annotation("__in")] UINT DataSize,
        [annotation("__in_bcount_opt(DataSize)")] const void* pData );
    HRESULT SetPrivateDataInterface(
        [annotation("__in")] REFGUID guid,
        [annotation("__in_opt")] const IUnknown* pData );
    
    D3D_FEATURE_LEVEL GetFeatureLevel();
    UINT GetCreationFlags();
    HRESULT GetDeviceRemovedReason();
    void GetImmediateContext( [annotation("__out")] ID3D11DeviceContext** ppImmediateContext );
    HRESULT SetExceptionMode( UINT RaiseFlags );
    UINT GetExceptionMode();
};


typedef enum D3D11_CREATE_DEVICE_FLAG
{
    D3D11_CREATE_DEVICE_SINGLETHREADED = 0x1,
    D3D11_CREATE_DEVICE_DEBUG = 0x2,
    D3D11_CREATE_DEVICE_SWITCH_TO_REF = 0x4,
    D3D11_CREATE_DEVICE_PREVENT_INTERNAL_THREADING_OPTIMIZATIONS = 0x8,
    D3D11_CREATE_DEVICE_BGRA_SUPPORT = 0x20,

} D3D11_CREATE_DEVICE_FLAG;

const UINT D3D11_SDK_VERSION = 7;

cpp_quote( "#include \"d3d10_1.h\"" ) // 10_1.h must be included before any other d3d10 headers that would include d3d10.h
cpp_quote( "#if !defined( D3D11_IGNORE_SDK_LAYERS ) ")
cpp_quote( "#include \"d3d11sdklayers.h\" ")
cpp_quote( "#endif ")
cpp_quote( "#include \"d3d10misc.h\" ")
cpp_quote( "#include \"d3d10shader.h\" ")
cpp_quote( "#include \"d3d10effect.h\" ")
cpp_quote( "#include \"d3d10_1shader.h\" ")

cpp_quote( "" )
cpp_quote( "///////////////////////////////////////////////////////////////////////////" )
cpp_quote( "// D3D11CreateDevice" )
cpp_quote( "// ------------------" )
cpp_quote( "//" )
cpp_quote( "// pAdapter" )
cpp_quote( "//      If NULL, D3D11CreateDevice will choose the primary adapter and" )
cpp_quote( "//      create a new instance from a temporarily created IDXGIFactory." )
cpp_quote( "//      If non-NULL, D3D11CreateDevice will register the appropriate" )
cpp_quote( "//      device, if necessary (via IDXGIAdapter::RegisterDrver), before" )
cpp_quote( "//      creating the device." )
cpp_quote( "// DriverType" )
cpp_quote( "//      Specifies the driver type to be created: hardware, reference or" )
cpp_quote( "//      null." )
cpp_quote( "// Software" )
cpp_quote( "//      HMODULE of a DLL implementing a software rasterizer. Must be NULL for" )
cpp_quote( "//      non-Software driver types." )
cpp_quote( "// Flags" )
cpp_quote( "//      Any of those documented for D3D11CreateDeviceAndSwapChain." )
cpp_quote( "// pFeatureLevels" )
cpp_quote( "//      Any of those documented for D3D11CreateDeviceAndSwapChain." )
cpp_quote( "// FeatureLevels" )
cpp_quote( "//      Size of feature levels array." )
cpp_quote( "// SDKVersion" )
cpp_quote( "//      SDK version. Use the D3D11_SDK_VERSION macro." )
cpp_quote( "// ppDevice" )
cpp_quote( "//      Pointer to returned interface. May be NULL." )
cpp_quote( "// pFeatureLevel" )
cpp_quote( "//      Pointer to returned feature level. May be NULL." )
cpp_quote( "// ppImmediateContext" )
cpp_quote( "//      Pointer to returned interface. May be NULL." )
cpp_quote( "//" )
cpp_quote( "// Return Values" )
cpp_quote( "//  Any of those documented for " )
cpp_quote( "//          CreateDXGIFactory1" )
cpp_quote( "//          IDXGIFactory::EnumAdapters" )
cpp_quote( "//          IDXGIAdapter::RegisterDriver" )
cpp_quote( "//          D3D11CreateDevice" )
cpp_quote( "//" )
cpp_quote( "///////////////////////////////////////////////////////////////////////////" )
cpp_quote( "typedef HRESULT (WINAPI* PFN_D3D11_CREATE_DEVICE)( __in_opt IDXGIAdapter*, " )
cpp_quote( "    D3D_DRIVER_TYPE, HMODULE, UINT, " )
cpp_quote( "    __in_ecount_opt( FeatureLevels ) CONST D3D_FEATURE_LEVEL*, " )
cpp_quote( "    UINT FeatureLevels, UINT, __out_opt ID3D11Device**, " )
cpp_quote( "    __out_opt D3D_FEATURE_LEVEL*, __out_opt ID3D11DeviceContext** );" )
cpp_quote( "" )
cpp_quote( "HRESULT WINAPI D3D11CreateDevice(" )
cpp_quote( "    __in_opt IDXGIAdapter* pAdapter," )
cpp_quote( "    D3D_DRIVER_TYPE DriverType," )
cpp_quote( "    HMODULE Software," )
cpp_quote( "    UINT Flags," )
cpp_quote( "    __in_ecount_opt( FeatureLevels ) CONST D3D_FEATURE_LEVEL* pFeatureLevels," )
cpp_quote( "    UINT FeatureLevels," )
cpp_quote( "    UINT SDKVersion," )
cpp_quote( "    __out_opt ID3D11Device** ppDevice," )
cpp_quote( "    __out_opt D3D_FEATURE_LEVEL* pFeatureLevel," )
cpp_quote( "    __out_opt ID3D11DeviceContext** ppImmediateContext );" )
cpp_quote( "" )
cpp_quote( "///////////////////////////////////////////////////////////////////////////" )
cpp_quote( "// D3D11CreateDeviceAndSwapChain" )
cpp_quote( "// ------------------------------" )
cpp_quote( "//" )
cpp_quote( "// ppAdapter" )
cpp_quote( "//      If NULL, D3D11CreateDevice will choose the primary adapter and " )
cpp_quote( "//      create a new instance from a temporarily created IDXGIFactory." )
cpp_quote( "//      If non-NULL, D3D11CreateDevice will register the appropriate" )
cpp_quote( "//      device, if necessary (via IDXGIAdapter::RegisterDrver), before" )
cpp_quote( "//      creating the device." )
cpp_quote( "// DriverType" )
cpp_quote( "//      Specifies the driver type to be created: hardware, reference or" )
cpp_quote( "//      null." )
cpp_quote( "// Software" )
cpp_quote( "//      HMODULE of a DLL implementing a software rasterizer. Must be NULL for" )
cpp_quote( "//      non-Software driver types." )
cpp_quote( "// Flags" )
cpp_quote( "//      Any of those documented for D3D11CreateDevice." )
cpp_quote( "// pFeatureLevels" )
cpp_quote( "//      Array of any of the following:" )
cpp_quote( "//          D3D_FEATURE_LEVEL_11_0" )
cpp_quote( "//          D3D_FEATURE_LEVEL_10_1" )
cpp_quote( "//          D3D_FEATURE_LEVEL_10_0" )
cpp_quote( "//          D3D_FEATURE_LEVEL_9_3" )
cpp_quote( "//          D3D_FEATURE_LEVEL_9_2" )
cpp_quote( "//          D3D_FEATURE_LEVEL_9_1" )
cpp_quote( "//      Order indicates sequence in which instantiation will be attempted. If" )
cpp_quote( "//      NULL, then the implied order is the same as previously listed (i.e. " )
cpp_quote( "//      prefer most features available). " )
cpp_quote( "// FeatureLevels" )
cpp_quote( "//      Size of feature levels array." )
cpp_quote( "// SDKVersion" )
cpp_quote( "//      SDK version. Use the D3D11_SDK_VERSION macro." )
cpp_quote( "// pSwapChainDesc" )
cpp_quote( "//      Swap chain description, may be NULL." )
cpp_quote( "// ppSwapChain" )
cpp_quote( "//      Pointer to returned interface. May be NULL." )
cpp_quote( "// ppDevice" )
cpp_quote( "//      Pointer to returned interface. May be NULL." )
cpp_quote( "// pFeatureLevel" )
cpp_quote( "//      Pointer to returned feature level. May be NULL." )
cpp_quote( "// ppImmediateContext" )
cpp_quote( "//      Pointer to returned interface. May be NULL." )
cpp_quote( "//" )
cpp_quote( "// Return Values" )
cpp_quote( "//  Any of those documented for " )
cpp_quote( "//          CreateDXGIFactory1" )
cpp_quote( "//          IDXGIFactory::EnumAdapters" )
cpp_quote( "//          IDXGIAdapter::RegisterDriver" )
cpp_quote( "//          D3D11CreateDevice" )
cpp_quote( "//          IDXGIFactory::CreateSwapChain" )
cpp_quote( "//" )
cpp_quote( "///////////////////////////////////////////////////////////////////////////" )
cpp_quote( "typedef HRESULT (WINAPI* PFN_D3D11_CREATE_DEVICE_AND_SWAP_CHAIN)( __in_opt IDXGIAdapter*, " )
cpp_quote( "    D3D_DRIVER_TYPE, HMODULE, UINT, " )
cpp_quote( "    __in_ecount_opt( FeatureLevels ) CONST D3D_FEATURE_LEVEL*, " )
cpp_quote( "    UINT FeatureLevels, UINT, __in_opt CONST DXGI_SWAP_CHAIN_DESC*, " )
cpp_quote( "    __out_opt IDXGISwapChain**, __out_opt ID3D11Device**, " )
cpp_quote( "    __out_opt D3D_FEATURE_LEVEL*, __out_opt ID3D11DeviceContext** );" )
cpp_quote( "" )
cpp_quote( "HRESULT WINAPI D3D11CreateDeviceAndSwapChain(" )
cpp_quote( "    __in_opt IDXGIAdapter* pAdapter," )
cpp_quote( "    D3D_DRIVER_TYPE DriverType," )
cpp_quote( "    HMODULE Software," )
cpp_quote( "    UINT Flags," )
cpp_quote( "    __in_ecount_opt( FeatureLevels ) CONST D3D_FEATURE_LEVEL* pFeatureLevels," )
cpp_quote( "    UINT FeatureLevels," )
cpp_quote( "    UINT SDKVersion," )
cpp_quote( "    __in_opt CONST DXGI_SWAP_CHAIN_DESC* pSwapChainDesc," )
cpp_quote( "    __out_opt IDXGISwapChain** ppSwapChain," )
cpp_quote( "    __out_opt ID3D11Device** ppDevice," )
cpp_quote( "    __out_opt D3D_FEATURE_LEVEL* pFeatureLevel," )
cpp_quote( "    __out_opt ID3D11DeviceContext** ppImmediateContext );" )
cpp_quote( "" )


cpp_quote( "DEFINE_GUID(IID_ID3D11DeviceChild,0x1841e5c8,0x16b0,0x489b,0xbc,0xc8,0x44,0xcf,0xb0,0xd5,0xde,0xae);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11DepthStencilState,0x03823efb,0x8d8f,0x4e1c,0x9a,0xa2,0xf6,0x4b,0xb2,0xcb,0xfd,0xf1);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11BlendState,0x75b68faa,0x347d,0x4159,0x8f,0x45,0xa0,0x64,0x0f,0x01,0xcd,0x9a);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11RasterizerState,0x9bb4ab81,0xab1a,0x4d8f,0xb5,0x06,0xfc,0x04,0x20,0x0b,0x6e,0xe7);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Resource,0xdc8e63f3,0xd12b,0x4952,0xb4,0x7b,0x5e,0x45,0x02,0x6a,0x86,0x2d);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Buffer,0x48570b85,0xd1ee,0x4fcd,0xa2,0x50,0xeb,0x35,0x07,0x22,0xb0,0x37);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Texture1D,0xf8fb5c27,0xc6b3,0x4f75,0xa4,0xc8,0x43,0x9a,0xf2,0xef,0x56,0x4c);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Texture2D,0x6f15aaf2,0xd208,0x4e89,0x9a,0xb4,0x48,0x95,0x35,0xd3,0x4f,0x9c);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Texture3D,0x037e866e,0xf56d,0x4357,0xa8,0xaf,0x9d,0xab,0xbe,0x6e,0x25,0x0e);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11View,0x839d1216,0xbb2e,0x412b,0xb7,0xf4,0xa9,0xdb,0xeb,0xe0,0x8e,0xd1);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11ShaderResourceView,0xb0e06fe0,0x8192,0x4e1a,0xb1,0xca,0x36,0xd7,0x41,0x47,0x10,0xb2);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11RenderTargetView,0xdfdba067,0x0b8d,0x4865,0x87,0x5b,0xd7,0xb4,0x51,0x6c,0xc1,0x64);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11DepthStencilView,0x9fdac92a,0x1876,0x48c3,0xaf,0xad,0x25,0xb9,0x4f,0x84,0xa9,0xb6);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11UnorderedAccessView,0x28acf509,0x7f5c,0x48f6,0x86,0x11,0xf3,0x16,0x01,0x0a,0x63,0x80);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11VertexShader,0x3b301d64,0xd678,0x4289,0x88,0x97,0x22,0xf8,0x92,0x8b,0x72,0xf3);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11HullShader,0x8e5c6061,0x628a,0x4c8e,0x82,0x64,0xbb,0xe4,0x5c,0xb3,0xd5,0xdd);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11DomainShader,0xf582c508,0x0f36,0x490c,0x99,0x77,0x31,0xee,0xce,0x26,0x8c,0xfa);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11GeometryShader,0x38325b96,0xeffb,0x4022,0xba,0x02,0x2e,0x79,0x5b,0x70,0x27,0x5c);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11PixelShader,0xea82e40d,0x51dc,0x4f33,0x93,0xd4,0xdb,0x7c,0x91,0x25,0xae,0x8c);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11ComputeShader,0x4f5b196e,0xc2bd,0x495e,0xbd,0x01,0x1f,0xde,0xd3,0x8e,0x49,0x69);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11InputLayout,0xe4819ddc,0x4cf0,0x4025,0xbd,0x26,0x5d,0xe8,0x2a,0x3e,0x07,0xb7);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11SamplerState,0xda6fea51,0x564c,0x4487,0x98,0x10,0xf0,0xd0,0xf9,0xb4,0xe3,0xa5);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Asynchronous,0x4b35d0cd,0x1e15,0x4258,0x9c,0x98,0x1b,0x13,0x33,0xf6,0xdd,0x3b);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Query,0xd6c00747,0x87b7,0x425e,0xb8,0x4d,0x44,0xd1,0x08,0x56,0x0a,0xfd);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Predicate,0x9eb576dd,0x9f77,0x4d86,0x81,0xaa,0x8b,0xab,0x5f,0xe4,0x90,0xe2);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Counter,0x6e8c49fb,0xa371,0x4770,0xb4,0x40,0x29,0x08,0x60,0x22,0xb7,0x41);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11ClassInstance,0xa6cd7faa,0xb0b7,0x4a2f,0x94,0x36,0x86,0x62,0xa6,0x57,0x97,0xcb);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11ClassLinkage,0xddf57cba,0x9543,0x46e4,0xa1,0x2b,0xf2,0x07,0xa0,0xfe,0x7f,0xed);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11CommandList,0xa24bc4d1,0x769e,0x43f7,0x80,0x13,0x98,0xff,0x56,0x6c,0x18,0xe2);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11DeviceContext,0xc0bfa96c,0xe089,0x44fb,0x8e,0xaf,0x26,0xf8,0x79,0x61,0x90,0xda);" )
cpp_quote( "DEFINE_GUID(IID_ID3D11Device,0xdb6f6ddb,0xac77,0x4e88,0x82,0x53,0x81,0x9d,0xf9,0xbb,0xf1,0x40);" )

