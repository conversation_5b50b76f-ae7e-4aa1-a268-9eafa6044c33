﻿#ifndef __SWNDSPY__H__
#define __SWNDSPY__H__

#define SWND_MAX_NAME  250
#define SWND_MAX_CLASS 50
#define SWND_MAX_XML   5000

#pragma pack(push, 1)
struct SWNDINFO
{
    DWORD swnd;
    RECT rcWnd;
    RECT rcClient;
    BOOL bVisible;
    int nID;
    wchar_t szName[SWND_MAX_NAME + 1];
    wchar_t szClassName[SWND_MAX_CLASS + 1];
    wchar_t szXmlStr[SWND_MAX_XML + 1];
};
#pragma pack(pop)
#endif // __SWNDSPY__H__