#include <souistd.h>
#include <SInitGuid.h>

DEFINE_SGUID(IID_ICtrl, 0xBCB520F9, 0x3C81, 0x4AD7, 0xAD, 0xe2, 0xFE, 0x72, 0xBD, 0x76, 0x10, 0x32);
DEFINE_SGUID(IID_IAcc<PERSON><PERSON><PERSON>, 0x727e4eab, 0xcffa, 0x4073, 0x8c, 0x1, 0x28, 0x15, 0x5, 0x3f, 0xa4, 0x4f);
DEFINE_SGUID(IID_ICornerPathEffect, 0x14b1ecba, 0x7e3a, 0x4dbc, 0xa9, 0xda, 0xd4, 0xf, 0x2a, 0x9e, 0x5e, 0xc3);
DEFINE_SGUID(IID_IDashPathEffect, 0x517fb1cf, 0x550e, 0x4e07, 0x82, 0x2a, 0x42, 0x51, 0x31, 0xa3, 0x2d, 0xe6);
DEFINE_SGUID(IID_IDiscretePathEffect, 0x90a2ee62, 0x3bc7, 0x45da, 0xae, 0x5, 0xb5, 0x13, 0x2, 0xcc, 0x43, 0x57);

DEFINE_SGUID(IID_IOsrPanel, 0x36d49c0a, 0xccba, 0x4238, 0x98, 0xe2, 0xb6, 0xac, 0xb1, 0x88, 0x24, 0xb5);
DEFINE_SGUID(IID_IItemPanel, 0xf78ca81b, 0x729b, 0x4fe5, 0xb3, 0xfe, 0x6c, 0x70, 0xad, 0x36, 0x30, 0x17);
DEFINE_SGUID(IID_IImageWnd, 0x310a0c73, 0x1dca, 0x4ac2, 0xb6, 0xc9, 0x56, 0x9b, 0xe, 0xa4, 0x4, 0x96);

DEFINE_SGUID(IID_IAnimateImgWnd, 0xc2378093, 0xcb34, 0x4811, 0x98, 0xd1, 0xa7, 0x21, 0x63, 0x7b, 0x3b, 0x87);

DEFINE_SGUID(IID_IProg, 0xa572106, 0x14e0, 0x48d7, 0x85, 0xc3, 0x3e, 0x8, 0xb9, 0xda, 0x85, 0xa8);
DEFINE_SGUID(IID_IPanel, 0x8de0a6c5, 0x9cbc, 0x465f, 0xb7, 0x84, 0xeb, 0xa4, 0xcb, 0x8a, 0xe6, 0xd9);
DEFINE_SGUID(IID_IScrollView, 0xf7a4a172, 0x31b2, 0x4597, 0x9d, 0xe5, 0xf6, 0x21, 0x60, 0x72, 0x37, 0x33);
DEFINE_SGUID(IID_IHeaderCtrl, 0xee19aa27, 0x7c36, 0x4426, 0x87, 0x0, 0x8f, 0xbc, 0xb0, 0xc7, 0x38, 0xf5);
DEFINE_SGUID(IID_IListView, 0x107ed2ea, 0x6c95, 0x4cec, 0xa3, 0x0, 0x2c, 0xe5, 0xab, 0xa, 0x7d, 0xdb);
DEFINE_SGUID(IID_IMcListView, 0xa45f148c, 0xf628, 0x4df7, 0xa6, 0xe, 0xb5, 0x1d, 0xa0, 0x1, 0x7b, 0x0);
DEFINE_SGUID(IID_ITreeView, 0x572ca529, 0xafff, 0x412c, 0x8a, 0x51, 0x20, 0x14, 0xa9, 0x80, 0x17, 0x39);
DEFINE_SGUID(IID_ITileView, 0x5586ea88, 0x3d4b, 0x4d5f, 0x8f, 0x71, 0x43, 0x6d, 0x43, 0x42, 0xd, 0x0);
DEFINE_SGUID(IID_IListBox, 0xb0b45363, 0x3d10, 0x4889, 0x94, 0x59, 0xa2, 0xa8, 0x4e, 0x37, 0x57, 0x5f);
DEFINE_SGUID(IID_IComboBase, 0x5fb30471, 0x54cb, 0x4db8, 0x91, 0x60, 0x31, 0xb5, 0x45, 0xe2, 0x8b, 0xc5);
DEFINE_SGUID(IID_IComboBox, 0xe7f9c5a6, 0xea49, 0x41c8, 0x9d, 0x8f, 0x70, 0x75, 0xcb, 0x96, 0x74, 0xea);
DEFINE_SGUID(IID_IComboView, 0x36dd3bad, 0x523b, 0x421a, 0x93, 0x11, 0x3d, 0xd0, 0xf0, 0xbf, 0x6, 0xd1);
DEFINE_SGUID(IID_IDateTimePicker, 0x846f25cd, 0xe0db, 0x49b0, 0x8f, 0x20, 0xb, 0xd7, 0x5, 0x38, 0xf6, 0x31);
DEFINE_SGUID(IID_ITreeCtrl, 0xa877b61c, 0x7e5e, 0x4716, 0x84, 0x23, 0x8d, 0xc6, 0x1e, 0xaf, 0x61, 0xe9);
DEFINE_SGUID(IID_IHotKeyCtrl, 0xe21767ac, 0x6a40, 0x45a2, 0x9e, 0x5c, 0xea, 0x5c, 0xdf, 0x51, 0x6c, 0x60);
DEFINE_SGUID(IID_IRichEdit, 0x7b9c2c3e, 0x3101, 0x4cda, 0x94, 0x36, 0xf8, 0x8d, 0x99, 0x93, 0xba, 0x5f);
DEFINE_SGUID(IID_ITabPage, 0xa32ed365, 0xe6b8, 0x4ada, 0xbe, 0x49, 0xbc, 0xf1, 0x27, 0xf9, 0x44, 0x27);
DEFINE_SGUID(IID_ITabCtrl, 0x17714866, 0x88b9, 0x480b, 0x83, 0xd1, 0xf0, 0x44, 0x48, 0x6d, 0x78, 0xd1);
DEFINE_SGUID(IID_IEdit, 0x71ccb156, 0x39dd, 0x4eee, 0x84, 0x2c, 0xc8, 0x36, 0x33, 0x68, 0xaf, 0x30);
DEFINE_SGUID(IID_ISpinButtonCtrl, 0x7f8df81c, 0x6c6e, 0x4572, 0xbd, 0xe6, 0x7, 0x57, 0xa4, 0xd0, 0x7c, 0x5f);
DEFINE_SGUID(IID_IIconWnd, 0x80e930e7, 0xbfc2, 0x4e5e, 0x8f, 0xfc, 0xa2, 0xf0, 0xb4, 0xec, 0x24, 0xe9);
DEFINE_SGUID(IID_IRealWnd, 0xedff5b4f, 0x8bf0, 0x46fb, 0x93, 0x99, 0xc9, 0x18, 0x59, 0xd5, 0xa1, 0xbc);
DEFINE_SGUID(IID_IStackView, 0x59d1145e, 0xc906, 0x404e, 0x81, 0x28, 0x29, 0xc7, 0xcc, 0x80, 0xe3, 0xdc);
