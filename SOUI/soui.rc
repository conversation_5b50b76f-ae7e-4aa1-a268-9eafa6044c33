// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "winres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Chinese (P.R.C.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
#ifdef _WIN32
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED
#pragma code_page(936)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

2 TEXTINCLUDE 
BEGIN
    "#include ""winres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\n"
    "\0"
END

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Version
//
#include "include/soui-version.h"


#define _STRING(x) #x

#define STRINGIFY(x) #x
#define VERSION_STR(v1, v2, v3, v4) STRINGIFY(v1) "," STRINGIFY(v2) "," STRINGIFY(v3) "," STRINGIFY(v4)
#define VERSION_DEF(x1, x2, x3, x4) (x1), (x2), (x3), (x4)

#define SOUI_VER_STR    VERSION_STR(SOUI_VER1, SOUI_VER2, SOUI_VER3, SOUI_VER4)
#define SOUI_VER_DEF    VERSION_DEF(SOUI_VER1, SOUI_VER2, SOUI_VER3, SOUI_VER4)

VS_VERSION_INFO VERSIONINFO
 FILEVERSION SOUI_VER_DEF
 PRODUCTVERSION SOUI_VER_DEF
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404b0"
        BEGIN
            VALUE "CompanyName", "SetoutSoft"
            VALUE "FileDescription", "soui"
            VALUE "FileVersion", SOUI_VER_STR
            VALUE "InternalName", "soui"
            VALUE "LegalCopyright", "Copyright (C) 2014"
            VALUE "OriginalFilename", "soui.dll"
            VALUE "ProductName", "soui"
            VALUE "ProductVersion", SOUI_VER_STR
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END

#endif    // Chinese (P.R.C.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

