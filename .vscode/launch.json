{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "(gdb) Launch",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build2/bin/fun_test",
            "args": ["50331648"],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}/build2/bin",
            "environment": [],
            "externalConsole": true,
            "MIMode": "gdb",
            "miDebuggerPath": "/usr/bin/gdb",
            "setupCommands": [
            {
             "description": "Enable pretty-printing for gdb",
             "text": "-enable-pretty-printing",
             "ignoreFailures": true
            }
            ]
        },
        {
      "name": "(lldb) 调试 C++ 程序",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build2/bin/fun_test",  // 替换为你的可执行文件路径
      "args": [],                         // 可选：程序参数
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}/build2/bin",
      "environment": [],
      "externalConsole": false,
      "MIMode": "lldb",
      "setupCommands": [
        {
          "description": "为 lldb 启用整齐打印",
          "text": "enable-pretty-printing",
          "ignoreFailures": true
        }
      ]
    },
        {
            "name": "(gdb) Attach",            
            "type": "cppdbg",
            "request": "attach",
            "program": "${workspaceFolder}/build/bin/fun_test",
            "processId": "${command:pickProcess}",
            "MIMode": "gdb"
          }
    ]
}