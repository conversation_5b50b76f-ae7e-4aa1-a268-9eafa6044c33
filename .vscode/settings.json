{"files.associations": {"*.tcc": "cpp", "utility": "cpp", "cstdint": "cpp", "chrono": "cpp", "random": "cpp", "limits": "cpp", "algorithm": "cpp", "mutex": "cpp", "shared_mutex": "cpp", "regex": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "valarray": "cpp", "cassert": "cpp", "cerrno": "cpp", "cfloat": "cpp", "climits": "cpp", "ios": "cpp", "locale": "cpp", "queue": "cpp", "stack": "cpp", "cstdbool": "cpp", "platform.h": "c", "strfun.h": "c", "lsdefs.h": "c", "lsdntext.h": "c", "cairo.h": "c", "charconv": "cpp", "optional": "cpp", "format": "cpp", "span": "cpp", "text_encoding": "cpp", "variant": "cpp", "__config": "cpp"}}