
#include "gtkdlghelper.h"
#ifdef ENABLE_GTK4
#include <stdint.h>
#include <string>
#include <list>
#include <string.h>
#include <gtk/gtk.h>
#ifdef GDK_WINDOWING_X11
#include <gdk/x11/gdkx.h>
#endif
#include <condition_variable>
#include <mutex>

#include "log.h"
#define kLogTag "gtk"

// GTK4 dialog response data structure
struct DialogResponse {
    bool completed = false;
    bool accepted = false;
    std::mutex mutex;
    std::condition_variable cv;

    // File dialog specific data
    std::list<std::string> selected_files;
    std::string selected_filter;

    // Color dialog specific data
    COLORREF selected_color = 0;
};

namespace Gtk{
static void Init(){
    static bool s_inited = false;
    if(s_inited) return;
    s_inited = true;
    gtk_init();
    SLOG_STMI()<<"init gtk4 done";
}

static char* substr(const char *src, int m, int n)
{
    int len = n - m;
    char *dest = (char*)malloc(sizeof(char) * (len + 1));
    for (int i = m; i < n && (*(src + i) != '\0'); i++) {
        *dest = *(src + i);
        dest++;
    }
    *dest = '\0';
    return dest - len;
}

static void parseString(GSList** list,
                      const char* str,
                      const char* delim)
{
    char *_str = strdup(str);
    char *token = strtok(_str, delim);
    while (token != NULL) {
        *list = g_slist_append(*list, (gpointer)strdup(token));
        token = strtok(NULL, delim);
    }
    free(_str);
}

static GtkWindow * native2GtkWindow(HWND winid){
    GdkDisplay * display = gdk_display_get_default();
    const char *display_name = gdk_display_get_name(display);
    if(strstr(display_name,"wayland")!=0)
    {
        SLOG_STMW()<<"native2GtkWindow failed for wayland display!";
        return nullptr;
    }

#ifdef GDK_WINDOWING_X11
    // GTK4中，需要使用GdkSurface而不是GdkWindow
    GdkSurface *gdk_surface = gdk_x11_surface_foreign_new_for_display(display, winid);
    if(!gdk_surface) return nullptr;

    // 创建一个临时的 GTK 窗口
    GtkWindow *gtk_wnd = GTK_WINDOW(gtk_window_new());
    gtk_widget_realize(GTK_WIDGET(gtk_wnd));  // 确保窗口被实现

    // GTK4中设置surface的方式不同，这里简化处理
    // 实际上GTK4中parent窗口的设置更多通过transient关系
    if (gdk_surface) {
        gtk_window_set_transient_for(gtk_wnd, GTK_WINDOW(gtk_widget_get_root(GTK_WIDGET(gtk_wnd))));
    }

    return gtk_wnd;
#else
    // 对于非X11环境，返回nullptr
    SLOG_STMW()<<"native2GtkWindow only supports X11 display!";
    return nullptr;
#endif
}

// GTK4 single file dialog callback
static void on_file_dialog_response(GObject* source_object, GAsyncResult* res, gpointer user_data) {
    DialogResponse* response = static_cast<DialogResponse*>(user_data);
    std::lock_guard<std::mutex> lock(response->mutex);

    GtkFileDialog* dialog = GTK_FILE_DIALOG(source_object);
    GError* error = nullptr;

    // Try to get the selected file
    GFile* file = gtk_file_dialog_open_finish(dialog, res, &error);
    if (file && !error) {
        char* path = g_file_get_path(file);
        if (path) {
            response->selected_files.push_back(std::string(path));
            g_free(path);
            response->accepted = true;
        }
        g_object_unref(file);
    }

    if (error) {
        g_error_free(error);
    }

    response->completed = true;
    response->cv.notify_one();
}

// GTK4 save file dialog callback
static void on_save_dialog_response(GObject* source_object, GAsyncResult* res, gpointer user_data) {
    DialogResponse* response = static_cast<DialogResponse*>(user_data);
    std::lock_guard<std::mutex> lock(response->mutex);

    GtkFileDialog* dialog = GTK_FILE_DIALOG(source_object);
    GError* error = nullptr;

    GFile* file = gtk_file_dialog_save_finish(dialog, res, &error);
    if (file && !error) {
        char* path = g_file_get_path(file);
        if (path) {
            response->selected_files.push_back(std::string(path));
            g_free(path);
            response->accepted = true;
        }
        g_object_unref(file);
    }

    if (error) {
        g_error_free(error);
    }

    response->completed = true;
    response->cv.notify_one();
}

// GTK4 folder dialog callback
static void on_folder_dialog_response(GObject* source_object, GAsyncResult* res, gpointer user_data) {
    DialogResponse* response = static_cast<DialogResponse*>(user_data);
    std::lock_guard<std::mutex> lock(response->mutex);

    GtkFileDialog* dialog = GTK_FILE_DIALOG(source_object);
    GError* error = nullptr;

    GFile* file = gtk_file_dialog_select_folder_finish(dialog, res, &error);
    if (file && !error) {
        char* path = g_file_get_path(file);
        if (path) {
            response->selected_files.push_back(std::string(path));
            g_free(path);
            response->accepted = true;
        }
        g_object_unref(file);
    }

    if (error) {
        g_error_free(error);
    }

    response->completed = true;
    response->cv.notify_one();
}

// GTK4 multiple files dialog callback
static void on_files_dialog_response(GObject* source_object, GAsyncResult* res, gpointer user_data) {
    DialogResponse* response = static_cast<DialogResponse*>(user_data);
    std::lock_guard<std::mutex> lock(response->mutex);

    GtkFileDialog* dialog = GTK_FILE_DIALOG(source_object);
    GError* error = nullptr;

    GListModel* files = gtk_file_dialog_open_multiple_finish(dialog, res, &error);
    if (files && !error) {
        guint n_files = g_list_model_get_n_items(files);
        for (guint i = 0; i < n_files; i++) {
            GFile* file = G_FILE(g_list_model_get_item(files, i));
            if (file) {
                char* path = g_file_get_path(file);
                if (path) {
                    response->selected_files.push_back(std::string(path));
                    g_free(path);
                }
                g_object_unref(file);
            }
        }
        if (n_files > 0) {
            response->accepted = true;
        }
        g_object_unref(files);
    }

    if (error) {
        g_error_free(error);
    }

    response->completed = true;
    response->cv.notify_one();
}

static bool nativeFileDialog(HWND parent,
                      DlgMode mode,
                      char*** filenames,
                      int* files_count,
                      const char* title,
                      const char* file,
                      const char* path,
                      const char* flt,
                      char** sel_filter,
                      bool sel_multiple)
{
    GtkWindow *gtk_parent = native2GtkWindow(parent);

    // 创建GTK4文件对话框
    GtkFileDialog *dialog = gtk_file_dialog_new();
    gtk_file_dialog_set_title(dialog, title);

    // 设置初始目录
    if (path && strlen(path) > 0) {
        GFile *initial_folder = g_file_new_for_path(path);
        gtk_file_dialog_set_initial_folder(dialog, initial_folder);
        g_object_unref(initial_folder);
    }

    // 设置初始文件名（仅对保存对话框有效）
    if (mode == DlgMode::SAVE && file && strlen(file) > 0) {
        gtk_file_dialog_set_initial_name(dialog, file);
    }

    // 处理文件过滤器
    if (mode != DlgMode::FOLDER && flt && strlen(flt) > 0) {
        GListStore *filters = g_list_store_new(GTK_TYPE_FILE_FILTER);

        GSList *list = NULL;
        parseString(&list, flt, ";;");

        for (guint i = 0; i < g_slist_length(list); i++) {
            if (char *flt_name = (char*)g_slist_nth(list, i)->data) {
                GtkFileFilter *filter = gtk_file_filter_new();

                char *start = strchr(flt_name, '(');
                char *end = strchr(flt_name, ')');
                char *short_flt_name = NULL;

                if (mode == DlgMode::OPEN && strlen(flt_name) > 255 && start != NULL) {
                    int end_index = (int)(start - flt_name - 1);
                    if (end_index > 0)
                        short_flt_name = substr(flt_name, 0, end_index);
                }

                gtk_file_filter_set_name(filter, short_flt_name ? short_flt_name : flt_name);
                if (short_flt_name)
                    free(short_flt_name);

                if (start != NULL && end != NULL) {
                    int start_index = (int)(start - flt_name);
                    int end_index = (int)(end - flt_name);
                    if (start_index < end_index) {
                        char *fltrs = substr(flt_name, start_index + 1, end_index);
                        GSList *flt_list = NULL;
                        parseString(&flt_list, fltrs, " ");
                        free(fltrs);

                        for (guint j = 0; j < g_slist_length(flt_list); j++) {
                            char *nm = (char*)g_slist_nth(flt_list, j)->data;
                            if (nm != NULL)
                                gtk_file_filter_add_pattern(filter, nm);
                        }
                        if (flt_list)
                            g_slist_free(flt_list);
                    }
                }

                g_list_store_append(filters, filter);
                g_object_unref(filter);
            }
        }

        if (g_list_model_get_n_items(G_LIST_MODEL(filters)) > 0) {
            gtk_file_dialog_set_filters(dialog, G_LIST_MODEL(filters));
        }

        g_object_unref(filters);
        if (list)
            g_slist_free(list);
    }

    // 创建响应数据结构
    DialogResponse response;

    // 根据模式和多选设置启动相应的对话框
    if (mode == DlgMode::FOLDER) {
        // 文件夹选择对话框
        gtk_file_dialog_select_folder(dialog, gtk_parent, nullptr, on_folder_dialog_response, &response);
    } else if (mode == DlgMode::SAVE) {
        // 保存文件对话框
        gtk_file_dialog_save(dialog, gtk_parent, nullptr, on_save_dialog_response, &response);
    } else if (sel_multiple) {
        // 多文件选择对话框
        gtk_file_dialog_open_multiple(dialog, gtk_parent, nullptr, on_files_dialog_response, &response);
    } else {
        // 单文件选择对话框
        gtk_file_dialog_open(dialog, gtk_parent, nullptr, on_file_dialog_response, &response);
    }

    // 等待对话框完成
    std::unique_lock<std::mutex> lock(response.mutex);
    response.cv.wait(lock, [&response] { return response.completed; });

    bool ret = response.accepted;

    if (ret && !response.selected_files.empty()) {
        *files_count = (int)response.selected_files.size();
        *filenames = (char**)calloc((size_t)(*files_count), sizeof(char*));

        int i = 0;
        for (const auto& file : response.selected_files) {
            (*filenames)[i] = strdup(file.c_str());
            i++;
        }
    } else {
        *files_count = 0;
        *filenames = nullptr;
    }

    // 清理
    g_object_unref(dialog);

    // 处理GTK事件队列
    while (g_main_context_pending(nullptr)) {
        g_main_context_iteration(nullptr, FALSE);
    }

    return ret;
}


bool openGtkFileChooser( std::list<std::string> &files,
                               HWND parent,
                               DlgMode mode,
                               const std::string &title,
                               const std::string &file,
                               const std::string &path,
                               const std::string &filter,
                               std::string *sel_filter,
                               bool sel_multiple)
{
    int pos= file.rfind("/");
    const std::string _file = (pos != -1) ?
                file.substr(pos + 1) : file;
    const std::string _path = (path.empty() && pos != -1) ?
                file.substr(0, pos) : path;

    char **filenames = nullptr;
    char *_sel_filter = (sel_filter) ? strdup(sel_filter->c_str()) : nullptr;
    int files_count = 0;
    bool ret = nativeFileDialog(parent,
                     mode,
                     &filenames,
                     &files_count,
                     title.c_str(),
                     _file.c_str(),
                     _path.c_str(),
                     filter.c_str(),
                     &_sel_filter,
                     sel_multiple);

    for (int i = 0; i < files_count; i++) {
        if (filenames[i]) {
            files.push_back(filenames[i]);
            free(filenames[i]);
        }
    }
    if (filenames) {
        free(filenames);
    }
    if (_sel_filter) {
        if (sel_filter)
            *sel_filter = _sel_filter;
        free(_sel_filter);
    }
    return ret;
}

// GTK4 color dialog callback
static void on_color_dialog_response(GObject* source_object, GAsyncResult* res, gpointer user_data) {
    DialogResponse* response = static_cast<DialogResponse*>(user_data);
    std::lock_guard<std::mutex> lock(response->mutex);

    GtkColorDialog* dialog = GTK_COLOR_DIALOG(source_object);
    GError* error = nullptr;

    GdkRGBA* color = gtk_color_dialog_choose_rgba_finish(dialog, res, &error);
    if (color && !error) {
        // 转换颜色格式
        BYTE r = (BYTE)(color->red * 255);
        BYTE g = (BYTE)(color->green * 255);
        BYTE b = (BYTE)(color->blue * 255);
        BYTE a = (BYTE)(color->alpha * 255);

        response->selected_color = RGBA(r, g, b, a);
        response->accepted = true;

        gdk_rgba_free(color);
    }

    if (error) {
        g_error_free(error);
    }

    response->completed = true;
    response->cv.notify_one();
}

static inline BYTE tobcolor(double v){
    return (BYTE)(v*255);
}

bool gtk_choose_color(HWND parent, COLORREF *out){
    GtkWindow *gtk_parent = native2GtkWindow(parent);

    // 创建GTK4颜色对话框
    GtkColorDialog *dialog = gtk_color_dialog_new();
    gtk_color_dialog_set_title(dialog, "Choose Color");
    gtk_color_dialog_set_modal(dialog, TRUE);

    // 设置初始颜色（如果需要的话）
    GdkRGBA initial_color;
    if (out && *out != 0) {
        initial_color.red = GetRValue(*out) / 255.0;
        initial_color.green = GetGValue(*out) / 255.0;
        initial_color.blue = GetBValue(*out) / 255.0;
        initial_color.alpha = 1.0; // 默认不透明
    } else {
        initial_color.red = 1.0;
        initial_color.green = 1.0;
        initial_color.blue = 1.0;
        initial_color.alpha = 1.0;
    }

    // 创建响应数据结构
    DialogResponse response;

    // 启动颜色选择对话框
    gtk_color_dialog_choose_rgba(dialog, gtk_parent, &initial_color, nullptr, on_color_dialog_response, &response);

    // 等待对话框完成
    std::unique_lock<std::mutex> lock(response.mutex);
    response.cv.wait(lock, [&response] { return response.completed; });

    bool ret = response.accepted;
    if (ret && out) {
        *out = response.selected_color;
    }

    // 清理
    g_object_unref(dialog);

    // 处理GTK事件队列
    while (g_main_context_pending(nullptr)) {
        g_main_context_iteration(nullptr, FALSE);
    }

    return ret;
}


}//end of ns Gtk

BOOL SChooseColor(HWND parent,const COLORREF initClr[16],COLORREF *out){
    Gtk::Init();
    return Gtk::gtk_choose_color(parent,out);
}

BOOL SGetOpenFileNameA(LPOPENFILENAMEA p, DlgMode mode)
{
    Gtk::Init();
    std::list<std::string> lstRet;
    bool ret = Gtk::openGtkFileChooser(lstRet, p->hwndOwner, (DlgMode)mode, "", "", p->lpstrInitialDir ? p->lpstrInitialDir : "", p->lpstrFilter ? p->lpstrFilter : "", NULL, p->Flags & OFN_ALLOWMULTISELECT);
    if (!ret || lstRet.empty())
        return FALSE;
    auto it = lstRet.begin();
    std::string &file = *it;
    int pos = file.rfind('/');
    if (!pos)
        return FALSE;
    if (p->lpstrFileTitle && p->nMaxFileTitle > file.length() - (pos + 1))
    {
        strcpy(p->lpstrFileTitle, file.c_str() + pos + 1);
    }
    p->nFileOffset = 0;
    if (p->Flags & OFN_ALLOWMULTISELECT)
    {
        std::stringstream ss;
        std::string path = file.substr(0, pos);
        ss << path;
        ss << "\0";
        p->nFileOffset = pos + 1;
        while (it != lstRet.end())
        {
            file = *it;
            if (file.length() > pos + 1)
            {
                ss << file.substr(pos + 1);
                ss << "\0";
            }
            it++;
        }
        ss << "\0";
        if (p->nMaxFile < ss.str().length())
        {
            SetLastError(SEC_E_INSUFFICIENT_MEMORY);
            return FALSE;
        }
        memcpy(p->lpstrFile, ss.str().c_str(), ss.str().length());
    }
    else
    {
        if (p->nMaxFile < lstRet.front().length())
        {
            SetLastError(SEC_E_INSUFFICIENT_MEMORY);
            return FALSE;
        }
        strcpy(p->lpstrFile, lstRet.front().c_str());
    }
    return TRUE;
}
#else
    BOOL SChooseColor(HWND parent,const COLORREF initClr[16],COLORREF *out){
        return FALSE;
    }

    BOOL SGetOpenFileNameA(LPOPENFILENAMEA p, DlgMode mode){
        return FALSE;
    }
#endif//EABLE_GTK