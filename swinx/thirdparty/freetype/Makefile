#
# FreeType 2 build system -- top-level Makefile
#


# Copyright (C) 1996-2024 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.


# Project names
#
PROJECT       := freetype
PROJECT_TITLE := FreeType

# The variable TOP_DIR holds the path to the topmost directory in the project
# engine source hierarchy.  If it is not defined, default it to `.'.
#
TOP_DIR ?= .

# The variable OBJ_DIR gives the location where object files and the
# FreeType library are built.
#
OBJ_DIR ?= $(TOP_DIR)/objs


include $(TOP_DIR)/builds/toplevel.mk

# EOF
