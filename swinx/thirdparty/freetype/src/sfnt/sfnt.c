/****************************************************************************
 *
 * sfnt.c
 *
 *   Single object library component.
 *
 * Copyright (C) 1996-2024 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#define FT_MAKE_OPTION_SINGLE_OBJECT

#include "pngshim.c"
#include "sfdriver.c"
#include "sfobjs.c"
#include "sfwoff.c"
#include "sfwoff2.c"
#include "ttbdf.c"
#include "ttcmap.c"
#include "ttcolr.c"
#include "ttcpal.c"
#include "ttsvg.c"

#include "ttgpos.c"
#include "ttkern.c"
#include "ttload.c"
#include "ttmtx.c"
#include "ttpost.c"
#include "ttsbit.c"
#include "woff2tags.c"


/* END */
