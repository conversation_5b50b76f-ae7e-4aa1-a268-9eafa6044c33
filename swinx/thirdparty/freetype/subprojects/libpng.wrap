[wrap-file]
directory = libpng-1.6.43
source_url = https://github.com/glennrp/libpng/archive/v1.6.43.tar.gz
source_filename = libpng-1.6.43.tar.gz
source_hash = fecc95b46cf05e8e3fc8a414750e0ba5aad00d89e9fdf175e94ff041caf1a03a
patch_filename = libpng_1.6.43-2_patch.zip
patch_url = https://wrapdb.mesonbuild.com/v2/libpng_1.6.43-2/get_patch
patch_hash = 49951297edf03e81d925ab03726555f09994ad1ed78fb539a269216430eef3da
source_fallback_url = https://github.com/mesonbuild/wrapdb/releases/download/libpng_1.6.43-2/libpng-1.6.43.tar.gz
wrapdb_version = 1.6.43-2

[provide]
libpng = libpng_dep
