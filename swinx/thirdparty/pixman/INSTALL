Installation Instructions
*************************

Copyright (C) 1994, 1995, 1996, 1999, 2000, 2001, 2002, 2004, 2005,
2006 Free Software Foundation, Inc.

This file is free documentation; the Free Software Foundation gives
unlimited permission to copy, distribute and modify it.

Basic Installation
==================

Briefly, the shell commands `meson setup build/; ninja -C build; ninja
-C build install` should configure, build, and install this package. The
following more-detailed instructions are generic; see the `README` file
for instructions specific to this package.

   Running `meson setup` attempts to guess correct values for various
system-dependent variables used during compilation.

The simplest way to compile this package is:

  1. `cd` to the directory containing the package's source code and type
     `meson setup build/` to configure the package for your system.

     While running, it prints some messages telling which features it
     is checking for.

  2. Type `ninja -C build` to compile the package.

  3. Optionally, type `ninja -C build test` to run any self-tests that
     come with the package.

  4. Type `ninja -C build install` to install the programs and any
     data files and documentation.

  5. You can remove the program binaries and object files from the
     source code directory by typing `ninja -C build clean`.
