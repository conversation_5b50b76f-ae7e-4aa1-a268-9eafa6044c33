# Makefile.in generated by automake 1.18.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2025 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

# Makefile.am, the source file for Makefile.in (and hence Makefile), is
#
# Copyright (c) 2018-2025 Cosmin Truta
# Copyright (c) 2004-2016 <PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h





VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
am__rm_f = rm -f $(am__rm_f_notfound)
am__rm_rf = rm -rf $(am__rm_f_notfound)
pkgdatadir = $(datadir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@ENABLE_TESTS_TRUE@check_PROGRAMS = pngtest$(EXEEXT) \
@ENABLE_TESTS_TRUE@	pngunknown$(EXEEXT) pngstest$(EXEEXT) \
@ENABLE_TESTS_TRUE@	pngvalid$(EXEEXT) pngimage$(EXEEXT) \
@ENABLE_TESTS_TRUE@	pngcp$(EXEEXT) $(am__EXEEXT_1)
@ENABLE_TESTS_TRUE@@HAVE_CLOCK_GETTIME_TRUE@am__append_1 = timepng
@ENABLE_TOOLS_TRUE@bin_PROGRAMS = pngfix$(EXEEXT) \
@ENABLE_TOOLS_TRUE@	png-fix-itxt$(EXEEXT)
@PNG_ARM_NEON_TRUE@am__append_2 = arm/arm_init.c\
@PNG_ARM_NEON_TRUE@	arm/filter_neon_intrinsics.c \
@PNG_ARM_NEON_TRUE@	arm/palette_neon_intrinsics.c

@PNG_MIPS_MSA_TRUE@am__append_3 = mips/mips_init.c\
@PNG_MIPS_MSA_TRUE@	mips/filter_msa_intrinsics.c

@PNG_MIPS_MMI_TRUE@@PNG_MIPS_MSA_FALSE@am__append_4 = mips/mips_init.c
@PNG_MIPS_MMI_TRUE@am__append_5 = mips/filter_mmi_inline_assembly.c
@PNG_INTEL_SSE_TRUE@am__append_6 = intel/intel_init.c\
@PNG_INTEL_SSE_TRUE@	intel/filter_sse2_intrinsics.c

@PNG_POWERPC_VSX_TRUE@am__append_7 = powerpc/powerpc_init.c\
@PNG_POWERPC_VSX_TRUE@        powerpc/filter_vsx_intrinsics.c


#   Versioned symbols and restricted exports
@HAVE_LD_VERSION_SCRIPT_TRUE@@HAVE_SOLARIS_LD_TRUE@am__append_8 = -Wl,-M -Wl,libpng.vers
@HAVE_LD_VERSION_SCRIPT_TRUE@@HAVE_SOLARIS_LD_FALSE@am__append_9 = -Wl,--version-script=libpng.vers
#   Only restricted exports when possible
@HAVE_LD_VERSION_SCRIPT_FALSE@am__append_10 = -export-symbols libpng.sym
@PNG_RISCV_RVV_TRUE@am__append_11 = libpng@PNGLIB_MAJOR@@<EMAIL>
@PNG_LOONGARCH_LSX_TRUE@am__append_12 = libpng@PNGLIB_MAJOR@@<EMAIL>
@DO_PNG_PREFIX_TRUE@am__append_13 = -DPNG_PREFIX='@PNG_PREFIX@'
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/scripts/autoconf/libtool.m4 \
	$(top_srcdir)/scripts/autoconf/ltoptions.m4 \
	$(top_srcdir)/scripts/autoconf/ltsugar.m4 \
	$(top_srcdir)/scripts/autoconf/ltversion.m4 \
	$(top_srcdir)/scripts/autoconf/lt~obsolete.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(pkginclude_HEADERS) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = config.h
CONFIG_CLEAN_FILES = libpng.pc libpng-config
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" \
	"$(DESTDIR)$(bindir)" "$(DESTDIR)$(man3dir)" \
	"$(DESTDIR)$(man5dir)" "$(DESTDIR)$(pkgconfigdir)" \
	"$(DESTDIR)$(pkgincludedir)" "$(DESTDIR)$(pkgincludedir)"
@ENABLE_TESTS_TRUE@@HAVE_CLOCK_GETTIME_TRUE@am__EXEEXT_1 =  \
@ENABLE_TESTS_TRUE@@HAVE_CLOCK_GETTIME_TRUE@	timepng$(EXEEXT)
PROGRAMS = $(bin_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
  || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
       $(am__cd) "$$dir" && echo $$files | $(am__xargs_n) 40 $(am__rm_f); }; \
  }
LTLIBRARIES = $(lib_LTLIBRARIES) $(noinst_LTLIBRARIES)
am__libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_SOURCES_DIST = png.c \
	pngerror.c pngget.c pngmem.c pngpread.c pngread.c pngrio.c \
	pngrtran.c pngrutil.c pngset.c pngtrans.c pngwio.c pngwrite.c \
	pngwtran.c pngwutil.c png.h pngconf.h pngdebug.h pnginfo.h \
	pngpriv.h pngstruct.h pngusr.dfa arm/arm_init.c \
	arm/filter_neon_intrinsics.c arm/palette_neon_intrinsics.c \
	mips/mips_init.c mips/filter_msa_intrinsics.c \
	mips/filter_mmi_inline_assembly.c intel/intel_init.c \
	intel/filter_sse2_intrinsics.c powerpc/powerpc_init.c \
	powerpc/filter_vsx_intrinsics.c
am__dirstamp = $(am__leading_dot)dirstamp
@PNG_ARM_NEON_TRUE@am__objects_1 = arm/arm_init.lo \
@PNG_ARM_NEON_TRUE@	arm/filter_neon_intrinsics.lo \
@PNG_ARM_NEON_TRUE@	arm/palette_neon_intrinsics.lo
@PNG_MIPS_MSA_TRUE@am__objects_2 = mips/mips_init.lo \
@PNG_MIPS_MSA_TRUE@	mips/filter_msa_intrinsics.lo
@PNG_MIPS_MMI_TRUE@@PNG_MIPS_MSA_FALSE@am__objects_3 =  \
@PNG_MIPS_MMI_TRUE@@PNG_MIPS_MSA_FALSE@	mips/mips_init.lo
@PNG_MIPS_MMI_TRUE@am__objects_4 = mips/filter_mmi_inline_assembly.lo
@PNG_INTEL_SSE_TRUE@am__objects_5 = intel/intel_init.lo \
@PNG_INTEL_SSE_TRUE@	intel/filter_sse2_intrinsics.lo
@PNG_POWERPC_VSX_TRUE@am__objects_6 = powerpc/powerpc_init.lo \
@PNG_POWERPC_VSX_TRUE@	powerpc/filter_vsx_intrinsics.lo
am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS = png.lo pngerror.lo \
	pngget.lo pngmem.lo pngpread.lo pngread.lo pngrio.lo \
	pngrtran.lo pngrutil.lo pngset.lo pngtrans.lo pngwio.lo \
	pngwrite.lo pngwtran.lo pngwutil.lo $(am__objects_1) \
	$(am__objects_2) $(am__objects_3) $(am__objects_4) \
	$(am__objects_5) $(am__objects_6)
nodist_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS =
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS =  \
	$(am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS) \
	$(nodist_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LDFLAGS) $(LDFLAGS) -o \
	$@
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_LIBADD =
am__libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_SOURCES_DIST =  \
	loongarch/loongarch_lsx_init.c \
	loongarch/filter_lsx_intrinsics.c
@PNG_LOONGARCH_LSX_TRUE@am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_OBJECTS = loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo \
@PNG_LOONGARCH_LSX_TRUE@	loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_OBJECTS =  \
	$(am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_OBJECTS)
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CCLD) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
@PNG_LOONGARCH_LSX_TRUE@am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_rpath =
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_LIBADD =
am__libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_SOURCES_DIST =  \
	riscv/riscv_init.c riscv/filter_rvv_intrinsics.c
@PNG_RISCV_RVV_TRUE@am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_OBJECTS = riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo \
@PNG_RISCV_RVV_TRUE@	riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_OBJECTS =  \
	$(am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_OBJECTS)
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CCLD) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_CFLAGS) \
	$(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
@PNG_RISCV_RVV_TRUE@am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_rpath =
am__png_fix_itxt_SOURCES_DIST = contrib/tools/png-fix-itxt.c
@ENABLE_TOOLS_TRUE@am_png_fix_itxt_OBJECTS =  \
@ENABLE_TOOLS_TRUE@	contrib/tools/png-fix-itxt.$(OBJEXT)
png_fix_itxt_OBJECTS = $(am_png_fix_itxt_OBJECTS)
png_fix_itxt_LDADD = $(LDADD)
am__pngcp_SOURCES_DIST = contrib/tools/pngcp.c
@ENABLE_TESTS_TRUE@am_pngcp_OBJECTS = contrib/tools/pngcp.$(OBJEXT)
pngcp_OBJECTS = $(am_pngcp_OBJECTS)
@ENABLE_TESTS_TRUE@pngcp_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__pngfix_SOURCES_DIST = contrib/tools/pngfix.c
@ENABLE_TOOLS_TRUE@am_pngfix_OBJECTS = contrib/tools/pngfix.$(OBJEXT)
pngfix_OBJECTS = $(am_pngfix_OBJECTS)
@ENABLE_TOOLS_TRUE@pngfix_DEPENDENCIES =  \
@ENABLE_TOOLS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__pngimage_SOURCES_DIST = contrib/libtests/pngimage.c
@ENABLE_TESTS_TRUE@am_pngimage_OBJECTS =  \
@ENABLE_TESTS_TRUE@	contrib/libtests/pngimage.$(OBJEXT)
pngimage_OBJECTS = $(am_pngimage_OBJECTS)
@ENABLE_TESTS_TRUE@pngimage_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__pngstest_SOURCES_DIST = contrib/libtests/pngstest.c
@ENABLE_TESTS_TRUE@am_pngstest_OBJECTS =  \
@ENABLE_TESTS_TRUE@	contrib/libtests/pngstest.$(OBJEXT)
pngstest_OBJECTS = $(am_pngstest_OBJECTS)
@ENABLE_TESTS_TRUE@pngstest_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__pngtest_SOURCES_DIST = pngtest.c
@ENABLE_TESTS_TRUE@am_pngtest_OBJECTS = pngtest.$(OBJEXT)
pngtest_OBJECTS = $(am_pngtest_OBJECTS)
@ENABLE_TESTS_TRUE@pngtest_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__pngunknown_SOURCES_DIST = contrib/libtests/pngunknown.c
@ENABLE_TESTS_TRUE@am_pngunknown_OBJECTS =  \
@ENABLE_TESTS_TRUE@	contrib/libtests/pngunknown.$(OBJEXT)
pngunknown_OBJECTS = $(am_pngunknown_OBJECTS)
@ENABLE_TESTS_TRUE@pngunknown_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__pngvalid_SOURCES_DIST = contrib/libtests/pngvalid.c
@ENABLE_TESTS_TRUE@am_pngvalid_OBJECTS =  \
@ENABLE_TESTS_TRUE@	contrib/libtests/pngvalid.$(OBJEXT)
pngvalid_OBJECTS = $(am_pngvalid_OBJECTS)
@ENABLE_TESTS_TRUE@pngvalid_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
am__timepng_SOURCES_DIST = contrib/libtests/timepng.c
@ENABLE_TESTS_TRUE@am_timepng_OBJECTS =  \
@ENABLE_TESTS_TRUE@	contrib/libtests/timepng.$(OBJEXT)
timepng_OBJECTS = $(am_timepng_OBJECTS)
@ENABLE_TESTS_TRUE@timepng_DEPENDENCIES =  \
@ENABLE_TESTS_TRUE@	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
SCRIPTS = $(bin_SCRIPTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/png.Plo ./$(DEPDIR)/pngerror.Plo \
	./$(DEPDIR)/pngget.Plo ./$(DEPDIR)/pngmem.Plo \
	./$(DEPDIR)/pngpread.Plo ./$(DEPDIR)/pngread.Plo \
	./$(DEPDIR)/pngrio.Plo ./$(DEPDIR)/pngrtran.Plo \
	./$(DEPDIR)/pngrutil.Plo ./$(DEPDIR)/pngset.Plo \
	./$(DEPDIR)/pngtest.Po ./$(DEPDIR)/pngtrans.Plo \
	./$(DEPDIR)/pngwio.Plo ./$(DEPDIR)/pngwrite.Plo \
	./$(DEPDIR)/pngwtran.Plo ./$(DEPDIR)/pngwutil.Plo \
	arm/$(DEPDIR)/arm_init.Plo \
	arm/$(DEPDIR)/filter_neon_intrinsics.Plo \
	arm/$(DEPDIR)/palette_neon_intrinsics.Plo \
	contrib/libtests/$(DEPDIR)/pngimage.Po \
	contrib/libtests/$(DEPDIR)/pngstest.Po \
	contrib/libtests/$(DEPDIR)/pngunknown.Po \
	contrib/libtests/$(DEPDIR)/pngvalid.Po \
	contrib/libtests/$(DEPDIR)/timepng.Po \
	contrib/tools/$(DEPDIR)/png-fix-itxt.Po \
	contrib/tools/$(DEPDIR)/pngcp.Po \
	contrib/tools/$(DEPDIR)/pngfix.Po \
	intel/$(DEPDIR)/filter_sse2_intrinsics.Plo \
	intel/$(DEPDIR)/intel_init.Plo \
	loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Plo \
	loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Plo \
	mips/$(DEPDIR)/filter_mmi_inline_assembly.Plo \
	mips/$(DEPDIR)/filter_msa_intrinsics.Plo \
	mips/$(DEPDIR)/mips_init.Plo \
	powerpc/$(DEPDIR)/filter_vsx_intrinsics.Plo \
	powerpc/$(DEPDIR)/powerpc_init.Plo \
	riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Plo \
	riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_SOURCES) \
	$(nodist_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_SOURCES) \
	$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_SOURCES) \
	$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_SOURCES) \
	$(png_fix_itxt_SOURCES) $(pngcp_SOURCES) $(pngfix_SOURCES) \
	$(pngimage_SOURCES) $(pngstest_SOURCES) $(pngtest_SOURCES) \
	$(pngunknown_SOURCES) $(pngvalid_SOURCES) $(timepng_SOURCES)
DIST_SOURCES =  \
	$(am__libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_SOURCES_DIST) \
	$(am__libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_SOURCES_DIST) \
	$(am__libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_SOURCES_DIST) \
	$(am__png_fix_itxt_SOURCES_DIST) $(am__pngcp_SOURCES_DIST) \
	$(am__pngfix_SOURCES_DIST) $(am__pngimage_SOURCES_DIST) \
	$(am__pngstest_SOURCES_DIST) $(am__pngtest_SOURCES_DIST) \
	$(am__pngunknown_SOURCES_DIST) $(am__pngvalid_SOURCES_DIST) \
	$(am__timepng_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
man3dir = $(mandir)/man3
man5dir = $(mandir)/man5
NROFF = nroff
MANS = $(dist_man_MANS)
DATA = $(pkgconfig_DATA)
HEADERS = $(nodist_pkginclude_HEADERS) $(pkginclude_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP) \
	config.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
AM_RECURSIVE_TARGETS = cscope check recheck
am__tty_colors_dummy = \
  mgn= red= grn= lgn= blu= brg= std=; \
  am__color_tests=no
am__tty_colors = { \
  $(am__tty_colors_dummy); \
  if test "X$(AM_COLOR_TESTS)" = Xno; then \
    am__color_tests=no; \
  elif test "X$(AM_COLOR_TESTS)" = Xalways; then \
    am__color_tests=yes; \
  elif test "X$$TERM" != Xdumb && { test -t 1; } 2>/dev/null; then \
    am__color_tests=yes; \
  fi; \
  if test $$am__color_tests = yes; then \
    red='[0;31m'; \
    grn='[0;32m'; \
    lgn='[1;32m'; \
    blu='[1;34m'; \
    mgn='[0;35m'; \
    brg='[1m'; \
    std='[m'; \
  fi; \
}
am__recheck_rx = ^[ 	]*:recheck:[ 	]*
am__global_test_result_rx = ^[ 	]*:global-test-result:[ 	]*
am__copy_in_global_log_rx = ^[ 	]*:copy-in-global-log:[ 	]*
# A command that, given a newline-separated list of test names on the
# standard input, print the name of the tests that are to be re-run
# upon "make recheck".
am__list_recheck_tests = $(AWK) '{ \
  recheck = 1; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
        { \
          if ((getline line2 < ($$0 ".log")) < 0) \
	    recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[nN][Oo]/) \
        { \
          recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[yY][eE][sS]/) \
        { \
          break; \
        } \
    }; \
  if (recheck) \
    print $$0; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# A command that, given a newline-separated list of test names on the
# standard input, create the global log from their .trs and .log files.
am__create_global_log = $(AWK) ' \
function fatal(msg) \
{ \
  print "fatal: making $@: " msg | "cat >&2"; \
  exit 1; \
} \
function rst_section(header) \
{ \
  print header; \
  len = length(header); \
  for (i = 1; i <= len; i = i + 1) \
    printf "="; \
  printf "\n\n"; \
} \
{ \
  copy_in_global_log = 1; \
  global_test_result = "RUN"; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
         fatal("failed to read from " $$0 ".trs"); \
      if (line ~ /$(am__global_test_result_rx)/) \
        { \
          sub("$(am__global_test_result_rx)", "", line); \
          sub("[ 	]*$$", "", line); \
          global_test_result = line; \
        } \
      else if (line ~ /$(am__copy_in_global_log_rx)[nN][oO]/) \
        copy_in_global_log = 0; \
    }; \
  if (copy_in_global_log) \
    { \
      rst_section(global_test_result ": " $$0); \
      while ((rc = (getline line < ($$0 ".log"))) != 0) \
      { \
        if (rc < 0) \
          fatal("failed to read from " $$0 ".log"); \
        print line; \
      }; \
      printf "\n"; \
    }; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# Restructured Text title.
am__rst_title = { sed 's/.*/   &   /;h;s/./=/g;p;x;s/ *$$//;p;g' && echo; }
# Solaris 10 'make', and several other traditional 'make' implementations,
# pass "-e" to $(SHELL), and POSIX 2008 even requires this.  Work around it
# by disabling -e (using the XSI extension "set +e") if it's set.
am__sh_e_setup = case $$- in *e*) set +e;; esac
# Default flags passed to test drivers.
am__common_driver_flags = \
  --color-tests "$$am__color_tests" \
  $$am__collect_skipped_logs \
  --enable-hard-errors "$$am__enable_hard_errors" \
  --expect-failure "$$am__expect_failure"
# To be inserted before the command running the test.  Creates the
# directory for the log if needed.  Stores in $dir the directory
# containing $f, in $tst the test, in $log the log.  Executes the
# developer-defined test setup AM_TESTS_ENVIRONMENT (if any), and
# passes TESTS_ENVIRONMENT.  Set up options for the wrapper that
# will run the test scripts (or their associated LOG_COMPILER, if
# thy have one).
am__check_pre = \
$(am__sh_e_setup);					\
$(am__vpath_adj_setup) $(am__vpath_adj)			\
$(am__tty_colors);					\
srcdir=$(srcdir); export srcdir;			\
case "$@" in						\
  */*) am__odir=`echo "./$@" | sed 's|/[^/]*$$||'`;;	\
    *) am__odir=.;; 					\
esac;							\
test "x$$am__odir" = x"." || test -d "$$am__odir" 	\
  || $(MKDIR_P) "$$am__odir" || exit $$?;		\
if test -f "./$$f"; then dir=./;			\
elif test -f "$$f"; then dir=;				\
else dir="$(srcdir)/"; fi;				\
tst=$$dir$$f; log='$@'; 				\
if test -n '$(IGNORE_SKIPPED_LOGS)'; then		\
  am__collect_skipped_logs='--collect-skipped-logs no';	\
else							\
  am__collect_skipped_logs='';				\
fi;							\
if test -n '$(DISABLE_HARD_ERRORS)'; then		\
  am__enable_hard_errors=no; 				\
else							\
  am__enable_hard_errors=yes; 				\
fi; 							\
case " $(XFAIL_TESTS) " in				\
  *[\ \	]$$f[\ \	]* | *[\ \	]$$dir$$f[\ \	]*) \
    am__expect_failure=yes;;				\
  *)							\
    am__expect_failure=no;;				\
esac; 							\
$(AM_TESTS_ENVIRONMENT) $(TESTS_ENVIRONMENT)
# A shell command to get the names of the tests scripts with any registered
# extension removed (i.e., equivalently, the names of the test logs, with
# the '.log' extension removed).  The result is saved in the shell variable
# '$bases'.  This honors runtime overriding of TESTS and TEST_LOGS.  Sadly,
# we cannot use something simpler, involving e.g., "$(TEST_LOGS:.log=)",
# since that might cause problem with VPATH rewrites for suffix-less tests.
# See also 'test-harness-vpath-rewrite.sh' and 'test-trs-basic.sh'.
am__set_TESTS_bases = \
  bases='$(TEST_LOGS)'; \
  bases=`for i in $$bases; do echo $$i; done | sed 's/\.log$$//'`; \
  bases=`echo $$bases`
AM_TESTSUITE_SUMMARY_HEADER = ' for $(PACKAGE_STRING)'
RECHECK_LOGS = $(TEST_LOGS)
TEST_SUITE_LOG = test-suite.log
TEST_EXTENSIONS = @EXEEXT@ .test
LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
LOG_COMPILE = $(LOG_COMPILER) $(AM_LOG_FLAGS) $(LOG_FLAGS)
am__set_b = \
  case '$@' in \
    */*) \
      case '$*' in \
        */*) b='$*';; \
          *) b=`echo '$@' | sed 's/\.log$$//'`; \
       esac;; \
    *) \
      b='$*';; \
  esac
am__test_logs1 = $(TESTS:=.log)
am__test_logs2 = $(am__test_logs1:@EXEEXT@.log=.log)
TEST_LOGS = $(am__test_logs2:.test.log=.log)
TEST_LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
TEST_LOG_COMPILE = $(TEST_LOG_COMPILER) $(AM_TEST_LOG_FLAGS) \
	$(TEST_LOG_FLAGS)
am__DIST_COMMON = $(dist_man_MANS) $(srcdir)/Makefile.in \
	$(srcdir)/config.h.in $(srcdir)/libpng-config.in \
	$(srcdir)/libpng.pc.in AUTHORS INSTALL README TODO compile \
	config.guess config.sub depcomp install-sh ltmain.sh missing \
	test-driver
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -700 -exec chmod u+rwx {} ';' \
      ; rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
DIST_ARCHIVES = $(distdir).tar.gz $(distdir).tar.xz
GZIP_ENV = -9
DIST_TARGETS = dist-xz dist-gzip
# Exists only to be overridden by the user if desired.
AM_DISTCHECK_DVI_TARGET = dvi
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = \
  find . \( -type f -a \! \
            \( -name .nfs* -o -name .smb* -o -name .__afs* \) \) -print

#distribute headers in /usr/include/libpng/*
pkgincludedir = $(includedir)/$(PNGLIB_BASENAME)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AS = @AS@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCAS = @CCAS@
CCASDEPMODE = @CCASDEPMODE@
CCASFLAGS = @CCASFLAGS@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@

# DFNCPP is normally just CPP - the C preprocessor - but on Solaris and maybe
# other operating systems (NeXT?) the C preprocessor selected by configure
# checks input tokens for validity - effectively it performs part of the ANSI-C
# parsing - and therefore fails with the .df files.  configure.ac has special
# checks for this and sets DFNCPP appropriately.
DFNCPP = @DFNCPP@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FILECMD = @FILECMD@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PNGLIB_MAJOR = @PNGLIB_MAJOR@
PNGLIB_MINOR = @PNGLIB_MINOR@
PNGLIB_RELEASE = @PNGLIB_RELEASE@
PNGLIB_VERSION = @PNGLIB_VERSION@

# PNG_COPTS give extra options for the C compiler to be used on all compilation
# steps (unless target_CFLAGS is specified; that will take precedence over
# AM_CFLAGS)
PNG_COPTS = @PNG_COPTS@
PNG_PREFIX = @PNG_PREFIX@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
SYMBOL_PREFIX = @SYMBOL_PREFIX@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__rm_f_notfound = @am__rm_f_notfound@
am__tar = @am__tar@
am__untar = @am__untar@
am__xargs_n = @am__xargs_n@

# generate the -config scripts if required
binconfigs = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@-config
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@

# pkg-config stuff, note that libpng.pc is always required in order
# to get the correct library
pkgconfigdir = @pkgconfigdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
PNGLIB_BASENAME = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@
ACLOCAL_AMFLAGS = -I scripts/autoconf

# This ensures that pnglibconf.h gets built at the start of 'make all' or
# 'make check', but it does not add dependencies to the individual programs,
# this is done below.
#
# IMPORTANT: always add the object modules of new programs to the list below
# because otherwise the sequence 'configure; make new-program' will *sometimes*
# result in the installed (system) pnglibconf.h being used and the result is
# always wrong and always very confusing.
BUILT_SOURCES = pnglibconf.h
@ENABLE_TESTS_TRUE@pngtest_SOURCES = pngtest.c
@ENABLE_TESTS_TRUE@pngtest_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TESTS_TRUE@pngvalid_SOURCES = contrib/libtests/pngvalid.c
@ENABLE_TESTS_TRUE@pngvalid_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TESTS_TRUE@pngstest_SOURCES = contrib/libtests/pngstest.c
@ENABLE_TESTS_TRUE@pngstest_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TESTS_TRUE@pngunknown_SOURCES = contrib/libtests/pngunknown.c
@ENABLE_TESTS_TRUE@pngunknown_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TESTS_TRUE@pngimage_SOURCES = contrib/libtests/pngimage.c
@ENABLE_TESTS_TRUE@pngimage_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TESTS_TRUE@timepng_SOURCES = contrib/libtests/timepng.c
@ENABLE_TESTS_TRUE@timepng_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TESTS_TRUE@pngcp_SOURCES = contrib/tools/pngcp.c
@ENABLE_TESTS_TRUE@pngcp_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TOOLS_TRUE@pngfix_SOURCES = contrib/tools/pngfix.c
@ENABLE_TOOLS_TRUE@pngfix_LDADD = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
@ENABLE_TOOLS_TRUE@png_fix_itxt_SOURCES = contrib/tools/png-fix-itxt.c

# Generally these are single line shell scripts to run a test with a particular
# set of parameters:
@ENABLE_TESTS_TRUE@TESTS = \
@ENABLE_TESTS_TRUE@   tests/pngtest-all\
@ENABLE_TESTS_TRUE@   tests/pngvalid-gamma-16-to-8 tests/pngvalid-gamma-alpha-mode\
@ENABLE_TESTS_TRUE@   tests/pngvalid-gamma-background tests/pngvalid-gamma-expand16-alpha-mode\
@ENABLE_TESTS_TRUE@   tests/pngvalid-gamma-expand16-background\
@ENABLE_TESTS_TRUE@   tests/pngvalid-gamma-expand16-transform tests/pngvalid-gamma-sbit\
@ENABLE_TESTS_TRUE@   tests/pngvalid-gamma-threshold tests/pngvalid-gamma-transform\
@ENABLE_TESTS_TRUE@   tests/pngvalid-progressive-size\
@ENABLE_TESTS_TRUE@   tests/pngvalid-progressive-interlace-standard\
@ENABLE_TESTS_TRUE@   tests/pngvalid-transform\
@ENABLE_TESTS_TRUE@   tests/pngvalid-progressive-standard tests/pngvalid-standard\
@ENABLE_TESTS_TRUE@   tests/pngstest-1.8 tests/pngstest-1.8-alpha tests/pngstest-linear\
@ENABLE_TESTS_TRUE@   tests/pngstest-linear-alpha tests/pngstest-none tests/pngstest-none-alpha\
@ENABLE_TESTS_TRUE@   tests/pngstest-sRGB tests/pngstest-sRGB-alpha tests/pngunknown-IDAT\
@ENABLE_TESTS_TRUE@   tests/pngunknown-discard tests/pngunknown-if-safe tests/pngunknown-sAPI\
@ENABLE_TESTS_TRUE@   tests/pngunknown-sTER tests/pngunknown-save tests/pngunknown-vpAg\
@ENABLE_TESTS_TRUE@   tests/pngimage-quick tests/pngimage-full


# man pages
dist_man_MANS = libpng.3 libpngpf.3 png.5
EXTRA_SCRIPTS = libpng-config libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@-config
bin_SCRIPTS = @binconfigs@

# rules to build libpng, only build the old library on request
lib_LTLIBRARIES = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la
# EXTRA_LTLIBRARIES= libpng.la
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_SOURCES = png.c pngerror.c \
	pngget.c pngmem.c pngpread.c pngread.c pngrio.c pngrtran.c \
	pngrutil.c pngset.c pngtrans.c pngwio.c pngwrite.c pngwtran.c \
	pngwutil.c png.h pngconf.h pngdebug.h pnginfo.h pngpriv.h \
	pngstruct.h pngusr.dfa $(am__append_2) $(am__append_3) \
	$(am__append_4) $(am__append_5) $(am__append_6) \
	$(am__append_7)
@PNG_LOONGARCH_LSX_TRUE@noinst_LTLIBRARIES = libpng@PNGLIB_MAJOR@@<EMAIL>
@PNG_RISCV_RVV_TRUE@noinst_LTLIBRARIES = libpng@PNGLIB_MAJOR@@<EMAIL>
@PNG_RISCV_RVV_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_SOURCES = riscv/riscv_init.c\
@PNG_RISCV_RVV_TRUE@        riscv/filter_rvv_intrinsics.c

@PNG_RISCV_RVV_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_CFLAGS = -march=rv64gv
@PNG_LOONGARCH_LSX_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LIBADD = libpng@PNGLIB_MAJOR@@<EMAIL>
@PNG_RISCV_RVV_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LIBADD = libpng@PNGLIB_MAJOR@@<EMAIL>
@PNG_LOONGARCH_LSX_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_SOURCES = loongarch/loongarch_lsx_init.c\
@PNG_LOONGARCH_LSX_TRUE@	loongarch/filter_lsx_intrinsics.c

@PNG_LOONGARCH_LSX_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_CFLAGS = -mlsx
nodist_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_SOURCES = pnglibconf.h
libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LDFLAGS = -no-undefined \
	-export-dynamic -version-number \
	@PNGLIB_MAJOR@@PNGLIB_MINOR@:@PNGLIB_RELEASE@:0 \
	$(am__append_8) $(am__append_9) $(am__append_10)
@HAVE_LD_VERSION_SCRIPT_FALSE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_DEPENDENCIES =  \
@HAVE_LD_VERSION_SCRIPT_FALSE@	libpng.sym $(am__append_11) \
@HAVE_LD_VERSION_SCRIPT_FALSE@	$(am__append_12)
@HAVE_LD_VERSION_SCRIPT_TRUE@libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_DEPENDENCIES =  \
@HAVE_LD_VERSION_SCRIPT_TRUE@	libpng.vers $(am__append_11) \
@HAVE_LD_VERSION_SCRIPT_TRUE@	$(am__append_12)
pkginclude_HEADERS = png.h pngconf.h
nodist_pkginclude_HEADERS = pnglibconf.h
pkgconfig_DATA = libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.pc

# Extra source distribution files, '${srcdir}' is used below to stop build files
# from those directories being included.  This only works if the configure is
# not done in the source directory!
EXTRA_DIST = \
	ANNOUNCE AUTHORS CHANGES INSTALL LICENSE README TODO TRADEMARK \
	pngtest.png pngbar.png pngnow.png pngbar.jpg autogen.sh \
	${srcdir}/ci ${srcdir}/contrib ${srcdir}/projects ${srcdir}/scripts \
	$(TESTS) $(XFAIL_TESTS) tests/pngstest \
	CMakeLists.txt example.c libpng-manual.txt

SCRIPT_CLEANFILES = scripts/*.out scripts/*.chk
CLEANFILES = *.tf? pngout.png libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.pc \
	libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@-config libpng.vers libpng.sym \
	check.new pnglibconf.h pngprefix.h symbols.new pngtest-log.txt \
	pnglibconf.out pnglibconf.c pnglibconf.pre pnglibconf.dfn \
	$(SCRIPT_CLEANFILES)

MAINTAINERCLEANFILES = Makefile.in aclocal.m4 config.guess config.h.in \
config.sub configure depcomp install-sh ltmain.sh missing

AM_CFLAGS = ${PNG_COPTS}
SUFFIXES = .chk .out

# We must use -DPNG_NO_USE_READ_MACROS here even when the library may actually
# be built with PNG_USE_READ_MACROS; this prevents the read macros from
# interfering with the symbol file format.
SYMBOL_CFLAGS = -DPNGLIB_LIBNAME='PNG@PNGLIB_MAJOR@@PNGLIB_MINOR@_0' \
	-DPNGLIB_VERSION='@PNGLIB_VERSION@' \
	-DSYMBOL_PREFIX='$(SYMBOL_PREFIX)' -DPNG_NO_USE_READ_MACROS \
	-DPNG_BUILDING_SYMBOL_TABLE $(am__append_13)

# EXT_LIST is a list of the possibly library directory extensions, this exists
# because we can't find a good way of discovering the file extensions that are
# actually installed on a given system, so instead we check for every extension
# we have seen.
EXT_LIST = a dll.a so so.@PNGLIB_MAJOR@@PNGLIB_MINOR@.@PNGLIB_RELEASE@ la sl dylib
all: $(BUILT_SOURCES) config.h
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .chk .out .c .lo .log .o .obj .test .test$(EXEEXT) .trs
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --foreign \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

config.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/config.h.in $(top_builddir)/config.status
	$(AM_V_at)rm -f stamp-h1
	$(AM_V_GEN)cd $(top_builddir) && $(SHELL) ./config.status config.h
$(srcdir)/config.h.in: @MAINTAINER_MODE_TRUE@ $(am__configure_deps) 
	$(AM_V_GEN)($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	$(AM_V_at)rm -f stamp-h1
	$(AM_V_at)touch $@

distclean-hdr:
	-rm -f config.h stamp-h1
libpng.pc: $(top_builddir)/config.status $(srcdir)/libpng.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
libpng-config: $(top_builddir)/config.status $(srcdir)/libpng-config.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && $(am__rm_f) $$files

clean-binPROGRAMS:
	$(am__rm_f) $(bin_PROGRAMS)
	test -z "$(EXEEXT)" || $(am__rm_f) $(bin_PROGRAMS:$(EXEEXT)=)

clean-checkPROGRAMS:
	$(am__rm_f) $(check_PROGRAMS)
	test -z "$(EXEEXT)" || $(am__rm_f) $(check_PROGRAMS:$(EXEEXT)=)

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-$(am__rm_f) $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	echo rm -f $${locs}; \
	$(am__rm_f) $${locs}

clean-noinstLTLIBRARIES:
	-$(am__rm_f) $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	echo rm -f $${locs}; \
	$(am__rm_f) $${locs}
arm/$(am__dirstamp):
	@$(MKDIR_P) arm
	@: >>arm/$(am__dirstamp)
arm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) arm/$(DEPDIR)
	@: >>arm/$(DEPDIR)/$(am__dirstamp)
arm/arm_init.lo: arm/$(am__dirstamp) arm/$(DEPDIR)/$(am__dirstamp)
arm/filter_neon_intrinsics.lo: arm/$(am__dirstamp) \
	arm/$(DEPDIR)/$(am__dirstamp)
arm/palette_neon_intrinsics.lo: arm/$(am__dirstamp) \
	arm/$(DEPDIR)/$(am__dirstamp)
mips/$(am__dirstamp):
	@$(MKDIR_P) mips
	@: >>mips/$(am__dirstamp)
mips/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) mips/$(DEPDIR)
	@: >>mips/$(DEPDIR)/$(am__dirstamp)
mips/mips_init.lo: mips/$(am__dirstamp) mips/$(DEPDIR)/$(am__dirstamp)
mips/filter_msa_intrinsics.lo: mips/$(am__dirstamp) \
	mips/$(DEPDIR)/$(am__dirstamp)
mips/filter_mmi_inline_assembly.lo: mips/$(am__dirstamp) \
	mips/$(DEPDIR)/$(am__dirstamp)
intel/$(am__dirstamp):
	@$(MKDIR_P) intel
	@: >>intel/$(am__dirstamp)
intel/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) intel/$(DEPDIR)
	@: >>intel/$(DEPDIR)/$(am__dirstamp)
intel/intel_init.lo: intel/$(am__dirstamp) \
	intel/$(DEPDIR)/$(am__dirstamp)
intel/filter_sse2_intrinsics.lo: intel/$(am__dirstamp) \
	intel/$(DEPDIR)/$(am__dirstamp)
powerpc/$(am__dirstamp):
	@$(MKDIR_P) powerpc
	@: >>powerpc/$(am__dirstamp)
powerpc/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) powerpc/$(DEPDIR)
	@: >>powerpc/$(DEPDIR)/$(am__dirstamp)
powerpc/powerpc_init.lo: powerpc/$(am__dirstamp) \
	powerpc/$(DEPDIR)/$(am__dirstamp)
powerpc/filter_vsx_intrinsics.lo: powerpc/$(am__dirstamp) \
	powerpc/$(DEPDIR)/$(am__dirstamp)

libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@.la: $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_DEPENDENCIES) $(EXTRA_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LINK) -rpath $(libdir) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_LIBADD) $(LIBS)
loongarch/$(am__dirstamp):
	@$(MKDIR_P) loongarch
	@: >>loongarch/$(am__dirstamp)
loongarch/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) loongarch/$(DEPDIR)
	@: >>loongarch/$(DEPDIR)/$(am__dirstamp)
loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo:  \
	loongarch/$(am__dirstamp) loongarch/$(DEPDIR)/$(am__dirstamp)
loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo:  \
	loongarch/$(am__dirstamp) loongarch/$(DEPDIR)/$(am__dirstamp)

libpng@PNGLIB_MAJOR@@<EMAIL>: $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_OBJECTS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_DEPENDENCIES) $(EXTRA_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_LINK) $(am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_rpath) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_OBJECTS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_LIBADD) $(LIBS)
riscv/$(am__dirstamp):
	@$(MKDIR_P) riscv
	@: >>riscv/$(am__dirstamp)
riscv/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) riscv/$(DEPDIR)
	@: >>riscv/$(DEPDIR)/$(am__dirstamp)
riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo:  \
	riscv/$(am__dirstamp) riscv/$(DEPDIR)/$(am__dirstamp)
riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo:  \
	riscv/$(am__dirstamp) riscv/$(DEPDIR)/$(am__dirstamp)

libpng@PNGLIB_MAJOR@@<EMAIL>: $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_OBJECTS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_DEPENDENCIES) $(EXTRA_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_LINK) $(am_libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_rpath) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_OBJECTS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_LIBADD) $(LIBS)
contrib/tools/$(am__dirstamp):
	@$(MKDIR_P) contrib/tools
	@: >>contrib/tools/$(am__dirstamp)
contrib/tools/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) contrib/tools/$(DEPDIR)
	@: >>contrib/tools/$(DEPDIR)/$(am__dirstamp)
contrib/tools/png-fix-itxt.$(OBJEXT): contrib/tools/$(am__dirstamp) \
	contrib/tools/$(DEPDIR)/$(am__dirstamp)

png-fix-itxt$(EXEEXT): $(png_fix_itxt_OBJECTS) $(png_fix_itxt_DEPENDENCIES) $(EXTRA_png_fix_itxt_DEPENDENCIES) 
	@rm -f png-fix-itxt$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(png_fix_itxt_OBJECTS) $(png_fix_itxt_LDADD) $(LIBS)
contrib/tools/pngcp.$(OBJEXT): contrib/tools/$(am__dirstamp) \
	contrib/tools/$(DEPDIR)/$(am__dirstamp)

pngcp$(EXEEXT): $(pngcp_OBJECTS) $(pngcp_DEPENDENCIES) $(EXTRA_pngcp_DEPENDENCIES) 
	@rm -f pngcp$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngcp_OBJECTS) $(pngcp_LDADD) $(LIBS)
contrib/tools/pngfix.$(OBJEXT): contrib/tools/$(am__dirstamp) \
	contrib/tools/$(DEPDIR)/$(am__dirstamp)

pngfix$(EXEEXT): $(pngfix_OBJECTS) $(pngfix_DEPENDENCIES) $(EXTRA_pngfix_DEPENDENCIES) 
	@rm -f pngfix$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngfix_OBJECTS) $(pngfix_LDADD) $(LIBS)
contrib/libtests/$(am__dirstamp):
	@$(MKDIR_P) contrib/libtests
	@: >>contrib/libtests/$(am__dirstamp)
contrib/libtests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) contrib/libtests/$(DEPDIR)
	@: >>contrib/libtests/$(DEPDIR)/$(am__dirstamp)
contrib/libtests/pngimage.$(OBJEXT): contrib/libtests/$(am__dirstamp) \
	contrib/libtests/$(DEPDIR)/$(am__dirstamp)

pngimage$(EXEEXT): $(pngimage_OBJECTS) $(pngimage_DEPENDENCIES) $(EXTRA_pngimage_DEPENDENCIES) 
	@rm -f pngimage$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngimage_OBJECTS) $(pngimage_LDADD) $(LIBS)
contrib/libtests/pngstest.$(OBJEXT): contrib/libtests/$(am__dirstamp) \
	contrib/libtests/$(DEPDIR)/$(am__dirstamp)

pngstest$(EXEEXT): $(pngstest_OBJECTS) $(pngstest_DEPENDENCIES) $(EXTRA_pngstest_DEPENDENCIES) 
	@rm -f pngstest$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngstest_OBJECTS) $(pngstest_LDADD) $(LIBS)

pngtest$(EXEEXT): $(pngtest_OBJECTS) $(pngtest_DEPENDENCIES) $(EXTRA_pngtest_DEPENDENCIES) 
	@rm -f pngtest$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngtest_OBJECTS) $(pngtest_LDADD) $(LIBS)
contrib/libtests/pngunknown.$(OBJEXT):  \
	contrib/libtests/$(am__dirstamp) \
	contrib/libtests/$(DEPDIR)/$(am__dirstamp)

pngunknown$(EXEEXT): $(pngunknown_OBJECTS) $(pngunknown_DEPENDENCIES) $(EXTRA_pngunknown_DEPENDENCIES) 
	@rm -f pngunknown$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngunknown_OBJECTS) $(pngunknown_LDADD) $(LIBS)
contrib/libtests/pngvalid.$(OBJEXT): contrib/libtests/$(am__dirstamp) \
	contrib/libtests/$(DEPDIR)/$(am__dirstamp)

pngvalid$(EXEEXT): $(pngvalid_OBJECTS) $(pngvalid_DEPENDENCIES) $(EXTRA_pngvalid_DEPENDENCIES) 
	@rm -f pngvalid$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pngvalid_OBJECTS) $(pngvalid_LDADD) $(LIBS)
contrib/libtests/timepng.$(OBJEXT): contrib/libtests/$(am__dirstamp) \
	contrib/libtests/$(DEPDIR)/$(am__dirstamp)

timepng$(EXEEXT): $(timepng_OBJECTS) $(timepng_DEPENDENCIES) $(EXTRA_timepng_DEPENDENCIES) 
	@rm -f timepng$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(timepng_OBJECTS) $(timepng_LDADD) $(LIBS)
install-binSCRIPTS: $(bin_SCRIPTS)
	@$(NORMAL_INSTALL)
	@list='$(bin_SCRIPTS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  if test -f "$$d$$p"; then echo "$$d$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n' \
	    -e 'h;s|.*|.|' \
	    -e 'p;x;s,.*/,,;$(transform)' | sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1; } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) { files[d] = files[d] " " $$1; \
	      if (++n[d] == $(am__install_max)) { \
		print "f", d, files[d]; n[d] = 0; files[d] = "" } } \
	    else { print "f", d "/" $$4, $$1 } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	     if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	     test -z "$$files" || { \
	       echo " $(INSTALL_SCRIPT) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	       $(INSTALL_SCRIPT) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	     } \
	; done

uninstall-binSCRIPTS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_SCRIPTS)'; test -n "$(bindir)" || exit 0; \
	files=`for p in $$list; do echo "$$p"; done | \
	       sed -e 's,.*/,,;$(transform)'`; \
	dir='$(DESTDIR)$(bindir)'; $(am__uninstall_files_from_dir)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f arm/*.$(OBJEXT)
	-rm -f arm/*.lo
	-rm -f contrib/libtests/*.$(OBJEXT)
	-rm -f contrib/tools/*.$(OBJEXT)
	-rm -f intel/*.$(OBJEXT)
	-rm -f intel/*.lo
	-rm -f loongarch/*.$(OBJEXT)
	-rm -f loongarch/*.lo
	-rm -f mips/*.$(OBJEXT)
	-rm -f mips/*.lo
	-rm -f powerpc/*.$(OBJEXT)
	-rm -f powerpc/*.lo
	-rm -f riscv/*.$(OBJEXT)
	-rm -f riscv/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/png.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngerror.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngget.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngmem.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngpread.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngread.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngrio.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngrtran.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngrutil.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngset.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngtest.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngtrans.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngwio.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngwrite.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngwtran.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pngwutil.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arm/$(DEPDIR)/arm_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arm/$(DEPDIR)/filter_neon_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@arm/$(DEPDIR)/palette_neon_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/libtests/$(DEPDIR)/pngimage.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/libtests/$(DEPDIR)/pngstest.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/libtests/$(DEPDIR)/pngunknown.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/libtests/$(DEPDIR)/pngvalid.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/libtests/$(DEPDIR)/timepng.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/tools/$(DEPDIR)/png-fix-itxt.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/tools/$(DEPDIR)/pngcp.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@contrib/tools/$(DEPDIR)/pngfix.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@intel/$(DEPDIR)/filter_sse2_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@intel/$(DEPDIR)/intel_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mips/$(DEPDIR)/filter_mmi_inline_assembly.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mips/$(DEPDIR)/filter_msa_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@mips/$(DEPDIR)/mips_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@powerpc/$(DEPDIR)/filter_vsx_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@powerpc/$(DEPDIR)/powerpc_init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@: >>$@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo: loongarch/loongarch_lsx_init.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_CFLAGS) $(CFLAGS) -MT loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo -MD -MP -MF loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Tpo -c -o loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo `test -f 'loongarch/loongarch_lsx_init.c' || echo '$(srcdir)/'`loongarch/loongarch_lsx_init.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Tpo loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='loongarch/loongarch_lsx_init.c' object='loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_CFLAGS) $(CFLAGS) -c -o loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.lo `test -f 'loongarch/loongarch_lsx_init.c' || echo '$(srcdir)/'`loongarch/loongarch_lsx_init.c

loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo: loongarch/filter_lsx_intrinsics.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_CFLAGS) $(CFLAGS) -MT loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo -MD -MP -MF loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Tpo -c -o loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo `test -f 'loongarch/filter_lsx_intrinsics.c' || echo '$(srcdir)/'`loongarch/filter_lsx_intrinsics.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Tpo loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='loongarch/filter_lsx_intrinsics.c' object='loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la_CFLAGS) $(CFLAGS) -c -o loongarch/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.lo `test -f 'loongarch/filter_lsx_intrinsics.c' || echo '$(srcdir)/'`loongarch/filter_lsx_intrinsics.c

riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo: riscv/riscv_init.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_CFLAGS) $(CFLAGS) -MT riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo -MD -MP -MF riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Tpo -c -o riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo `test -f 'riscv/riscv_init.c' || echo '$(srcdir)/'`riscv/riscv_init.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Tpo riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='riscv/riscv_init.c' object='riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_CFLAGS) $(CFLAGS) -c -o riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.lo `test -f 'riscv/riscv_init.c' || echo '$(srcdir)/'`riscv/riscv_init.c

riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo: riscv/filter_rvv_intrinsics.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_CFLAGS) $(CFLAGS) -MT riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo -MD -MP -MF riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Tpo -c -o riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo `test -f 'riscv/filter_rvv_intrinsics.c' || echo '$(srcdir)/'`riscv/filter_rvv_intrinsics.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Tpo riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='riscv/filter_rvv_intrinsics.c' object='riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la_CFLAGS) $(CFLAGS) -c -o riscv/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.lo `test -f 'riscv/filter_rvv_intrinsics.c' || echo '$(srcdir)/'`riscv/filter_rvv_intrinsics.c

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf arm/.libs arm/_libs
	-rm -rf intel/.libs intel/_libs
	-rm -rf loongarch/.libs loongarch/_libs
	-rm -rf mips/.libs mips/_libs
	-rm -rf powerpc/.libs powerpc/_libs
	-rm -rf riscv/.libs riscv/_libs

distclean-libtool:
	-rm -f libtool config.lt
install-man3: $(dist_man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(dist_man_MANS)'; \
	test -n "$(man3dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man3dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man3dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.3[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^3][0-9a-z]*$$,3,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man3dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man3dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man3dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man3dir)" || exit $$?; }; \
	done; }

uninstall-man3:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man3dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(dist_man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.3[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^3][0-9a-z]*$$,3,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man3dir)'; $(am__uninstall_files_from_dir)
install-man5: $(dist_man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(dist_man_MANS)'; \
	test -n "$(man5dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man5dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man5dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.5[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^5][0-9a-z]*$$,5,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man5dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man5dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man5dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man5dir)" || exit $$?; }; \
	done; }

uninstall-man5:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man5dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(dist_man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.5[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^5][0-9a-z]*$$,5,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man5dir)'; $(am__uninstall_files_from_dir)
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-nodist_pkgincludeHEADERS: $(nodist_pkginclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nodist_pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(pkgincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(pkgincludedir)" || exit $$?; \
	done

uninstall-nodist_pkgincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nodist_pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgincludedir)'; $(am__uninstall_files_from_dir)
install-pkgincludeHEADERS: $(pkginclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(pkgincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(pkgincludedir)" || exit $$?; \
	done

uninstall-pkgincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgincludedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files

# Recover from deleted '.trs' file; this should ensure that
# "rm -f foo.log; make foo.trs" re-run 'foo.test', and re-create
# both 'foo.log' and 'foo.trs'.  Break the recipe in two subshells
# to avoid problems with "make -n".
.log.trs:
	rm -f $< $@
	$(MAKE) $(AM_MAKEFLAGS) $<

# Leading 'am--fnord' is there to ensure the list of targets does not
# expand to empty, as could happen e.g. with make check TESTS=''.
am--fnord $(TEST_LOGS) $(TEST_LOGS:.log=.trs): $(am__force_recheck)
am--force-recheck:
	@:
$(TEST_SUITE_LOG): $(TEST_LOGS)
	@$(am__set_TESTS_bases); \
	am__f_ok () { test -f "$$1" && test -r "$$1"; }; \
	redo_bases=`for i in $$bases; do \
	              am__f_ok $$i.trs && am__f_ok $$i.log || echo $$i; \
	            done`; \
	if test -n "$$redo_bases"; then \
	  redo_logs=`for i in $$redo_bases; do echo $$i.log; done`; \
	  redo_results=`for i in $$redo_bases; do echo $$i.trs; done`; \
	  if $(am__make_dryrun); then :; else \
	    rm -f $$redo_logs && rm -f $$redo_results || exit 1; \
	  fi; \
	fi; \
	if test -n "$$am__remaking_logs"; then \
	  echo "fatal: making $(TEST_SUITE_LOG): possible infinite" \
	       "recursion detected" >&2; \
	elif test -n "$$redo_logs"; then \
	  am__remaking_logs=yes $(MAKE) $(AM_MAKEFLAGS) $$redo_logs; \
	fi; \
	if $(am__make_dryrun); then :; else \
	  st=0;  \
	  errmsg="fatal: making $(TEST_SUITE_LOG): failed to create"; \
	  for i in $$redo_bases; do \
	    test -f $$i.trs && test -r $$i.trs \
	      || { echo "$$errmsg $$i.trs" >&2; st=1; }; \
	    test -f $$i.log && test -r $$i.log \
	      || { echo "$$errmsg $$i.log" >&2; st=1; }; \
	  done; \
	  test $$st -eq 0 || exit 1; \
	fi
	@$(am__sh_e_setup); $(am__tty_colors); $(am__set_TESTS_bases); \
	ws='[ 	]'; \
	results=`for b in $$bases; do echo $$b.trs; done`; \
	test -n "$$results" || results=/dev/null; \
	all=`  grep "^$$ws*:test-result:"           $$results | wc -l`; \
	pass=` grep "^$$ws*:test-result:$$ws*PASS"  $$results | wc -l`; \
	fail=` grep "^$$ws*:test-result:$$ws*FAIL"  $$results | wc -l`; \
	skip=` grep "^$$ws*:test-result:$$ws*SKIP"  $$results | wc -l`; \
	xfail=`grep "^$$ws*:test-result:$$ws*XFAIL" $$results | wc -l`; \
	xpass=`grep "^$$ws*:test-result:$$ws*XPASS" $$results | wc -l`; \
	error=`grep "^$$ws*:test-result:$$ws*ERROR" $$results | wc -l`; \
	if test `expr $$fail + $$xpass + $$error` -eq 0; then \
	  success=true; \
	else \
	  success=false; \
	fi; \
	br='==================='; br=$$br$$br$$br$$br; \
	result_count () \
	{ \
	    if test x"$$1" = x"--maybe-color"; then \
	      maybe_colorize=yes; \
	    elif test x"$$1" = x"--no-color"; then \
	      maybe_colorize=no; \
	    else \
	      echo "$@: invalid 'result_count' usage" >&2; exit 4; \
	    fi; \
	    shift; \
	    desc=$$1 count=$$2; \
	    if test $$maybe_colorize = yes && test $$count -gt 0; then \
	      color_start=$$3 color_end=$$std; \
	    else \
	      color_start= color_end=; \
	    fi; \
	    echo "$${color_start}# $$desc $$count$${color_end}"; \
	}; \
	create_testsuite_report () \
	{ \
	  result_count $$1 "TOTAL:" $$all   "$$brg"; \
	  result_count $$1 "PASS: " $$pass  "$$grn"; \
	  result_count $$1 "SKIP: " $$skip  "$$blu"; \
	  result_count $$1 "XFAIL:" $$xfail "$$lgn"; \
	  result_count $$1 "FAIL: " $$fail  "$$red"; \
	  result_count $$1 "XPASS:" $$xpass "$$red"; \
	  result_count $$1 "ERROR:" $$error "$$mgn"; \
	}; \
	output_system_information () \
	{ \
          echo;                                     \
	  { uname -a | $(AWK) '{                    \
  printf "System information (uname -a):";          \
  for (i = 1; i < NF; ++i)                          \
    {                                               \
      if (i != 2)                                   \
        printf " %s", $$i;                          \
    }                                               \
  printf "\n";                                      \
}'; } 2>&1;                                         \
	  if test -r /etc/os-release; then          \
	    echo "Distribution information (/etc/os-release):"; \
	    sed 8q /etc/os-release;                 \
	  elif test -r /etc/issue; then             \
	    echo "Distribution information (/etc/issue):";      \
	    cat /etc/issue;                         \
	  fi;                                       \
	}; \
	please_report () \
	{ \
echo "Some test(s) failed.  Please report this to $(PACKAGE_BUGREPORT),";    \
echo "together with the test-suite.log file (gzipped) and your system";      \
echo "information.  Thanks.";                                                \
	}; \
	{								\
	  echo "$(PACKAGE_STRING): $(subdir)/$(TEST_SUITE_LOG)" |	\
	    $(am__rst_title);						\
	  create_testsuite_report --no-color;				\
	  output_system_information;                                    \
	  echo;								\
	  echo ".. contents:: :depth: 2";				\
	  echo;								\
	  for b in $$bases; do echo $$b; done				\
	    | $(am__create_global_log);					\
	} >$(TEST_SUITE_LOG).tmp || exit 1;				\
	mv $(TEST_SUITE_LOG).tmp $(TEST_SUITE_LOG);			\
	if $$success; then						\
	  col="$$grn";							\
	 else								\
	  col="$$red";							\
	  test x"$$VERBOSE" = x || cat $(TEST_SUITE_LOG);		\
	fi;								\
	echo "$${col}$$br$${std}"; 					\
	echo "$${col}Testsuite summary"$(AM_TESTSUITE_SUMMARY_HEADER)"$${std}";	\
	echo "$${col}$$br$${std}"; 					\
	create_testsuite_report --maybe-color;				\
	echo "$$col$$br$$std";						\
	if $$success; then :; else					\
	  echo "$${col}See $(subdir)/$(TEST_SUITE_LOG) for debugging.$${std}";\
	  if test -n "$(PACKAGE_BUGREPORT)"; then			\
	    please_report | sed -e "s/^/$${col}/" -e s/'$$'/"$${std}"/; \
	  fi;								\
	  echo "$$col$$br$$std";					\
	fi;								\
	$$success || exit 1

check-TESTS: $(check_PROGRAMS)
	@$(am__rm_f) $(RECHECK_LOGS)
	@$(am__rm_f) $(RECHECK_LOGS:.log=.trs)
	@$(am__rm_f) $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	log_list=`echo $$log_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) TEST_LOGS="$$log_list"; \
	exit $$?;
recheck: all $(check_PROGRAMS)
	@$(am__rm_f) $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	bases=`for i in $$bases; do echo $$i; done \
	         | $(am__list_recheck_tests)` || exit 1; \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	log_list=`echo $$log_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) \
	        am__force_recheck=am--force-recheck \
	        TEST_LOGS="$$log_list"; \
	exit $$?
tests/pngtest-all.log: tests/pngtest-all
	@p='tests/pngtest-all'; \
	b='tests/pngtest-all'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-16-to-8.log: tests/pngvalid-gamma-16-to-8
	@p='tests/pngvalid-gamma-16-to-8'; \
	b='tests/pngvalid-gamma-16-to-8'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-alpha-mode.log: tests/pngvalid-gamma-alpha-mode
	@p='tests/pngvalid-gamma-alpha-mode'; \
	b='tests/pngvalid-gamma-alpha-mode'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-background.log: tests/pngvalid-gamma-background
	@p='tests/pngvalid-gamma-background'; \
	b='tests/pngvalid-gamma-background'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-expand16-alpha-mode.log: tests/pngvalid-gamma-expand16-alpha-mode
	@p='tests/pngvalid-gamma-expand16-alpha-mode'; \
	b='tests/pngvalid-gamma-expand16-alpha-mode'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-expand16-background.log: tests/pngvalid-gamma-expand16-background
	@p='tests/pngvalid-gamma-expand16-background'; \
	b='tests/pngvalid-gamma-expand16-background'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-expand16-transform.log: tests/pngvalid-gamma-expand16-transform
	@p='tests/pngvalid-gamma-expand16-transform'; \
	b='tests/pngvalid-gamma-expand16-transform'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-sbit.log: tests/pngvalid-gamma-sbit
	@p='tests/pngvalid-gamma-sbit'; \
	b='tests/pngvalid-gamma-sbit'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-threshold.log: tests/pngvalid-gamma-threshold
	@p='tests/pngvalid-gamma-threshold'; \
	b='tests/pngvalid-gamma-threshold'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-gamma-transform.log: tests/pngvalid-gamma-transform
	@p='tests/pngvalid-gamma-transform'; \
	b='tests/pngvalid-gamma-transform'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-progressive-size.log: tests/pngvalid-progressive-size
	@p='tests/pngvalid-progressive-size'; \
	b='tests/pngvalid-progressive-size'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-progressive-interlace-standard.log: tests/pngvalid-progressive-interlace-standard
	@p='tests/pngvalid-progressive-interlace-standard'; \
	b='tests/pngvalid-progressive-interlace-standard'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-transform.log: tests/pngvalid-transform
	@p='tests/pngvalid-transform'; \
	b='tests/pngvalid-transform'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-progressive-standard.log: tests/pngvalid-progressive-standard
	@p='tests/pngvalid-progressive-standard'; \
	b='tests/pngvalid-progressive-standard'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngvalid-standard.log: tests/pngvalid-standard
	@p='tests/pngvalid-standard'; \
	b='tests/pngvalid-standard'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-1.8.log: tests/pngstest-1.8
	@p='tests/pngstest-1.8'; \
	b='tests/pngstest-1.8'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-1.8-alpha.log: tests/pngstest-1.8-alpha
	@p='tests/pngstest-1.8-alpha'; \
	b='tests/pngstest-1.8-alpha'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-linear.log: tests/pngstest-linear
	@p='tests/pngstest-linear'; \
	b='tests/pngstest-linear'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-linear-alpha.log: tests/pngstest-linear-alpha
	@p='tests/pngstest-linear-alpha'; \
	b='tests/pngstest-linear-alpha'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-none.log: tests/pngstest-none
	@p='tests/pngstest-none'; \
	b='tests/pngstest-none'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-none-alpha.log: tests/pngstest-none-alpha
	@p='tests/pngstest-none-alpha'; \
	b='tests/pngstest-none-alpha'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-sRGB.log: tests/pngstest-sRGB
	@p='tests/pngstest-sRGB'; \
	b='tests/pngstest-sRGB'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngstest-sRGB-alpha.log: tests/pngstest-sRGB-alpha
	@p='tests/pngstest-sRGB-alpha'; \
	b='tests/pngstest-sRGB-alpha'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-IDAT.log: tests/pngunknown-IDAT
	@p='tests/pngunknown-IDAT'; \
	b='tests/pngunknown-IDAT'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-discard.log: tests/pngunknown-discard
	@p='tests/pngunknown-discard'; \
	b='tests/pngunknown-discard'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-if-safe.log: tests/pngunknown-if-safe
	@p='tests/pngunknown-if-safe'; \
	b='tests/pngunknown-if-safe'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-sAPI.log: tests/pngunknown-sAPI
	@p='tests/pngunknown-sAPI'; \
	b='tests/pngunknown-sAPI'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-sTER.log: tests/pngunknown-sTER
	@p='tests/pngunknown-sTER'; \
	b='tests/pngunknown-sTER'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-save.log: tests/pngunknown-save
	@p='tests/pngunknown-save'; \
	b='tests/pngunknown-save'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngunknown-vpAg.log: tests/pngunknown-vpAg
	@p='tests/pngunknown-vpAg'; \
	b='tests/pngunknown-vpAg'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngimage-quick.log: tests/pngimage-quick
	@p='tests/pngimage-quick'; \
	b='tests/pngimage-quick'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/pngimage-full.log: tests/pngimage-full
	@p='tests/pngimage-full'; \
	b='tests/pngimage-full'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
.test.log:
	@p='$<'; \
	$(am__set_b); \
	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
@am__EXEEXT_TRUE@.test$(EXEEXT).log:
@am__EXEEXT_TRUE@	@p='$<'; \
@am__EXEEXT_TRUE@	$(am__set_b); \
@am__EXEEXT_TRUE@	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
@am__EXEEXT_TRUE@	--log-file $$b.log --trs-file $$b.trs \
@am__EXEEXT_TRUE@	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
@am__EXEEXT_TRUE@	"$$tst" $(AM_TESTS_FD_REDIRECT)

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	$(AM_V_at)$(MKDIR_P) "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	$(MAKE) $(AM_MAKEFLAGS) \
	  top_distdir="$(top_distdir)" distdir="$(distdir)" \
	  dist-hook
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-bzip3: distdir
	tardir=$(distdir) && $(am__tar) | bzip3 -c >$(distdir).tar.bz3
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)
dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-zstd: distdir
	tardir=$(distdir) && $(am__tar) | zstd -c $${ZSTD_CLEVEL-$${ZSTD_OPT--19}} >$(distdir).tar.zst
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.bz3*) \
	  bzip3 -dc $(distdir).tar.bz3 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	*.tar.zst*) \
	  zstd -dc $(distdir).tar.zst | $(am__untar) ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) $(AM_DISTCHECK_DVI_TARGET) \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) $(check_PROGRAMS)
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(SCRIPTS) $(MANS) $(DATA) \
		$(HEADERS) config.h
install-binPROGRAMS: install-libLTLIBRARIES

install-checkPROGRAMS: install-libLTLIBRARIES

installdirs:
	for dir in "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" "$(DESTDIR)$(man3dir)" "$(DESTDIR)$(man5dir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(pkgincludedir)" "$(DESTDIR)$(pkgincludedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:
	-$(am__rm_f) $(TEST_LOGS)
	-$(am__rm_f) $(TEST_LOGS:.log=.trs)
	-$(am__rm_f) $(TEST_SUITE_LOG)

clean-generic:
	-$(am__rm_f) $(CLEANFILES)

distclean-generic:
	-$(am__rm_f) $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || $(am__rm_f) $(CONFIG_CLEAN_VPATH_FILES)
	-$(am__rm_f) arm/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) arm/$(am__dirstamp)
	-$(am__rm_f) contrib/libtests/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) contrib/libtests/$(am__dirstamp)
	-$(am__rm_f) contrib/tools/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) contrib/tools/$(am__dirstamp)
	-$(am__rm_f) intel/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) intel/$(am__dirstamp)
	-$(am__rm_f) loongarch/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) loongarch/$(am__dirstamp)
	-$(am__rm_f) mips/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) mips/$(am__dirstamp)
	-$(am__rm_f) powerpc/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) powerpc/$(am__dirstamp)
	-$(am__rm_f) riscv/$(DEPDIR)/$(am__dirstamp)
	-$(am__rm_f) riscv/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-$(am__rm_f) $(BUILT_SOURCES)
	-$(am__rm_f) $(MAINTAINERCLEANFILES)
@DO_INSTALL_LIBPNG_CONFIG_FALSE@@DO_INSTALL_LINKS_FALSE@install-exec-hook:
@DO_INSTALL_LIBPNG_PC_FALSE@@DO_INSTALL_LINKS_FALSE@install-data-hook:
@DO_INSTALL_LIBPNG_CONFIG_FALSE@@DO_INSTALL_LIBPNG_PC_FALSE@@DO_INSTALL_LINKS_FALSE@uninstall-hook:
clean: clean-am

clean-am: clean-binPROGRAMS clean-checkPROGRAMS clean-generic \
	clean-libLTLIBRARIES clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -f ./$(DEPDIR)/png.Plo
	-rm -f ./$(DEPDIR)/pngerror.Plo
	-rm -f ./$(DEPDIR)/pngget.Plo
	-rm -f ./$(DEPDIR)/pngmem.Plo
	-rm -f ./$(DEPDIR)/pngpread.Plo
	-rm -f ./$(DEPDIR)/pngread.Plo
	-rm -f ./$(DEPDIR)/pngrio.Plo
	-rm -f ./$(DEPDIR)/pngrtran.Plo
	-rm -f ./$(DEPDIR)/pngrutil.Plo
	-rm -f ./$(DEPDIR)/pngset.Plo
	-rm -f ./$(DEPDIR)/pngtest.Po
	-rm -f ./$(DEPDIR)/pngtrans.Plo
	-rm -f ./$(DEPDIR)/pngwio.Plo
	-rm -f ./$(DEPDIR)/pngwrite.Plo
	-rm -f ./$(DEPDIR)/pngwtran.Plo
	-rm -f ./$(DEPDIR)/pngwutil.Plo
	-rm -f arm/$(DEPDIR)/arm_init.Plo
	-rm -f arm/$(DEPDIR)/filter_neon_intrinsics.Plo
	-rm -f arm/$(DEPDIR)/palette_neon_intrinsics.Plo
	-rm -f contrib/libtests/$(DEPDIR)/pngimage.Po
	-rm -f contrib/libtests/$(DEPDIR)/pngstest.Po
	-rm -f contrib/libtests/$(DEPDIR)/pngunknown.Po
	-rm -f contrib/libtests/$(DEPDIR)/pngvalid.Po
	-rm -f contrib/libtests/$(DEPDIR)/timepng.Po
	-rm -f contrib/tools/$(DEPDIR)/png-fix-itxt.Po
	-rm -f contrib/tools/$(DEPDIR)/pngcp.Po
	-rm -f contrib/tools/$(DEPDIR)/pngfix.Po
	-rm -f intel/$(DEPDIR)/filter_sse2_intrinsics.Plo
	-rm -f intel/$(DEPDIR)/intel_init.Plo
	-rm -f loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Plo
	-rm -f loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Plo
	-rm -f mips/$(DEPDIR)/filter_mmi_inline_assembly.Plo
	-rm -f mips/$(DEPDIR)/filter_msa_intrinsics.Plo
	-rm -f mips/$(DEPDIR)/mips_init.Plo
	-rm -f powerpc/$(DEPDIR)/filter_vsx_intrinsics.Plo
	-rm -f powerpc/$(DEPDIR)/powerpc_init.Plo
	-rm -f riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Plo
	-rm -f riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-man install-nodist_pkgincludeHEADERS \
	install-pkgconfigDATA install-pkgincludeHEADERS
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) install-data-hook
install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS install-binSCRIPTS \
	install-libLTLIBRARIES
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) install-exec-hook
install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man: install-man3 install-man5

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
	-rm -f ./$(DEPDIR)/png.Plo
	-rm -f ./$(DEPDIR)/pngerror.Plo
	-rm -f ./$(DEPDIR)/pngget.Plo
	-rm -f ./$(DEPDIR)/pngmem.Plo
	-rm -f ./$(DEPDIR)/pngpread.Plo
	-rm -f ./$(DEPDIR)/pngread.Plo
	-rm -f ./$(DEPDIR)/pngrio.Plo
	-rm -f ./$(DEPDIR)/pngrtran.Plo
	-rm -f ./$(DEPDIR)/pngrutil.Plo
	-rm -f ./$(DEPDIR)/pngset.Plo
	-rm -f ./$(DEPDIR)/pngtest.Po
	-rm -f ./$(DEPDIR)/pngtrans.Plo
	-rm -f ./$(DEPDIR)/pngwio.Plo
	-rm -f ./$(DEPDIR)/pngwrite.Plo
	-rm -f ./$(DEPDIR)/pngwtran.Plo
	-rm -f ./$(DEPDIR)/pngwutil.Plo
	-rm -f arm/$(DEPDIR)/arm_init.Plo
	-rm -f arm/$(DEPDIR)/filter_neon_intrinsics.Plo
	-rm -f arm/$(DEPDIR)/palette_neon_intrinsics.Plo
	-rm -f contrib/libtests/$(DEPDIR)/pngimage.Po
	-rm -f contrib/libtests/$(DEPDIR)/pngstest.Po
	-rm -f contrib/libtests/$(DEPDIR)/pngunknown.Po
	-rm -f contrib/libtests/$(DEPDIR)/pngvalid.Po
	-rm -f contrib/libtests/$(DEPDIR)/timepng.Po
	-rm -f contrib/tools/$(DEPDIR)/png-fix-itxt.Po
	-rm -f contrib/tools/$(DEPDIR)/pngcp.Po
	-rm -f contrib/tools/$(DEPDIR)/pngfix.Po
	-rm -f intel/$(DEPDIR)/filter_sse2_intrinsics.Plo
	-rm -f intel/$(DEPDIR)/intel_init.Plo
	-rm -f loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-filter_lsx_intrinsics.Plo
	-rm -f loongarch/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@lsx_la-loongarch_lsx_init.Plo
	-rm -f mips/$(DEPDIR)/filter_mmi_inline_assembly.Plo
	-rm -f mips/$(DEPDIR)/filter_msa_intrinsics.Plo
	-rm -f mips/$(DEPDIR)/mips_init.Plo
	-rm -f powerpc/$(DEPDIR)/filter_vsx_intrinsics.Plo
	-rm -f powerpc/$(DEPDIR)/powerpc_init.Plo
	-rm -f riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-filter_rvv_intrinsics.Plo
	-rm -f riscv/$(DEPDIR)/libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@rvv_la-riscv_init.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-binSCRIPTS \
	uninstall-libLTLIBRARIES uninstall-man \
	uninstall-nodist_pkgincludeHEADERS uninstall-pkgconfigDATA \
	uninstall-pkgincludeHEADERS
	@$(NORMAL_INSTALL)
	$(MAKE) $(AM_MAKEFLAGS) uninstall-hook
uninstall-man: uninstall-man3 uninstall-man5

.MAKE: all check check-am install install-am install-data-am \
	install-exec install-exec-am install-strip uninstall-am

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles am--refresh check \
	check-TESTS check-am clean clean-binPROGRAMS \
	clean-checkPROGRAMS clean-cscope clean-generic \
	clean-libLTLIBRARIES clean-libtool clean-noinstLTLIBRARIES \
	cscope cscopelist-am ctags ctags-am dist dist-all dist-bzip2 \
	dist-bzip3 dist-gzip dist-hook dist-lzip dist-shar dist-tarZ \
	dist-xz dist-zip dist-zstd distcheck distclean \
	distclean-compile distclean-generic distclean-hdr \
	distclean-libtool distclean-tags distcleancheck distdir \
	distuninstallcheck dvi dvi-am html html-am info info-am \
	install install-am install-binPROGRAMS install-binSCRIPTS \
	install-data install-data-am install-data-hook install-dvi \
	install-dvi-am install-exec install-exec-am install-exec-hook \
	install-html install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-man install-man3 install-man5 \
	install-nodist_pkgincludeHEADERS install-pdf install-pdf-am \
	install-pkgconfigDATA install-pkgincludeHEADERS install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am recheck tags tags-am \
	uninstall uninstall-am uninstall-binPROGRAMS \
	uninstall-binSCRIPTS uninstall-hook uninstall-libLTLIBRARIES \
	uninstall-man uninstall-man3 uninstall-man5 \
	uninstall-nodist_pkgincludeHEADERS uninstall-pkgconfigDATA \
	uninstall-pkgincludeHEADERS

.PRECIOUS: Makefile


$(PNGLIB_BASENAME).pc: libpng.pc
	cp libpng.pc $@

$(PNGLIB_BASENAME)-config: libpng-config
	cp libpng-config $@

scripts/sym.out scripts/vers.out: png.h pngconf.h pnglibconf.h
scripts/prefix.out: png.h pngconf.h pnglibconf.out
scripts/symbols.out: png.h pngconf.h $(srcdir)/scripts/pnglibconf.h.prebuilt
scripts/intprefix.out: pnglibconf.h

libpng.sym: scripts/sym.out
	rm -f $@
	cp $? $@
libpng.vers: scripts/vers.out
	rm -f $@
	cp $? $@

# Rename functions in scripts/prefix.out with a PNG_PREFIX prefix.
# Rename macros in scripts/macro.lst from PNG_PREFIXpng_ to PNG_ (the actual
# implementation of the macro).
@DO_PNG_PREFIX_TRUE@pnglibconf.h: pnglibconf.out scripts/prefix.out scripts/macro.lst
@DO_PNG_PREFIX_TRUE@	rm -f $@
@DO_PNG_PREFIX_TRUE@	$(AWK) 's==0 && NR>1{print prev}\
@DO_PNG_PREFIX_TRUE@	   s==0{prev=$$0}\
@DO_PNG_PREFIX_TRUE@	   s==1{print "#define", $$1, "@PNG_PREFIX@" $$1}\
@DO_PNG_PREFIX_TRUE@	   s==2{print "#define @PNG_PREFIX@png_" $$1, "PNG_" $$1}\
@DO_PNG_PREFIX_TRUE@	   END{print prev}' s=0 pnglibconf.out s=1 scripts/prefix.out\
@DO_PNG_PREFIX_TRUE@	   s=2 ${srcdir}/scripts/macro.lst >pnglibconf.tf8
@DO_PNG_PREFIX_TRUE@	mv pnglibconf.tf8 $@

@DO_PNG_PREFIX_TRUE@pngprefix.h: scripts/intprefix.out
@DO_PNG_PREFIX_TRUE@	rm -f pngprefix.tf1
@DO_PNG_PREFIX_TRUE@	$(AWK) '{print "#define", $$1, "@PNG_PREFIX@" $$1}' $? >pngprefix.tf1
@DO_PNG_PREFIX_TRUE@	mv pngprefix.tf1 $@
@DO_PNG_PREFIX_FALSE@pnglibconf.h: pnglibconf.out
@DO_PNG_PREFIX_FALSE@	rm -f $@
@DO_PNG_PREFIX_FALSE@	cp $? $@

@DO_PNG_PREFIX_FALSE@pngprefix.h: # is empty
@DO_PNG_PREFIX_FALSE@	:>$@

$(srcdir)/scripts/pnglibconf.h.prebuilt:
	@echo "Attempting to build $@" >&2
	@echo "This is a machine generated file, but if you want to make" >&2
	@echo "a new one simply make 'scripts/pnglibconf.out', copy that" >&2
	@echo "AND set PNG_ZLIB_VERNUM to 0 (you MUST do this)" >&2
	@exit 1

# The following is necessary to ensure that the local pnglibconf.h is used, not
# an installed one (this can happen immediately after on a clean system if
# 'make test' is the first thing the user does.)  Only files which include
# one of the png source files (typically png.h or pngpriv.h) need to be listed
# here:
pngtest.o: pnglibconf.h

contrib/libtests/makepng.o: pnglibconf.h
contrib/libtests/pngstest.o: pnglibconf.h
contrib/libtests/pngunknown.o: pnglibconf.h
contrib/libtests/pngimage.o: pnglibconf.h
contrib/libtests/pngvalid.o: pnglibconf.h
contrib/libtests/readpng.o: pnglibconf.h
contrib/libtests/tarith.o: pnglibconf.h
contrib/libtests/timepng.o: pnglibconf.h

contrib/tools/makesRGB.o: pnglibconf.h
contrib/tools/pngfix.o: pnglibconf.h
contrib/tools/pngcp.o: pnglibconf.h

.c.out:
	rm -f $@ $*.tf[12]
	test -d scripts || mkdir scripts || test -d scripts
	$(DFNCPP) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES)\
	    $(CPPFLAGS) $(SYMBOL_CFLAGS) $< > $*.tf1
	$(AWK) -f "${srcdir}/scripts/dfn.awk" out="$*.tf2" $*.tf1 1>&2
	rm -f $*.tf1
	mv $*.tf2 $@

# The .c file for pnglibconf.h is machine generated
pnglibconf.c: scripts/pnglibconf.dfa scripts/options.awk pngconf.h pngusr.dfa $(DFA_XTRA)
	rm -f $@ $*.tf[45]
	$(AWK) -f ${srcdir}/scripts/options.awk out=$*.tf4 version=search\
	    ${srcdir}/pngconf.h ${srcdir}/scripts/pnglibconf.dfa\
	    ${srcdir}/pngusr.dfa $(DFA_XTRA) 1>&2
	$(AWK) -f ${srcdir}/scripts/options.awk out=$*.tf5 $*.tf4 1>&2
	rm $*.tf4
	mv $*.tf5 $@

# Symbol checks (.def and .out files should match)
scripts/symbols.chk: scripts/checksym.awk scripts/symbols.def scripts/symbols.out

.out.chk:
	rm -f $@ $*.new
	$(AWK) -f ${srcdir}/scripts/checksym.awk ${srcdir}/scripts/${*F}.def\
	    of="$*.new" $< >&2
	mv $*.new $@

# used on demand to regenerate the standard header, CPPFLAGS should
# be empty - no non-standard defines
scripts/pnglibconf.c: scripts/pnglibconf.dfa scripts/options.awk pngconf.h
	rm -f $@ pnglibconf.tf[67]
	test -z "$(CPPFLAGS)"
	echo "com @PNGLIB_VERSION@ STANDARD API DEFINITION" |\
	$(AWK) -f ${srcdir}/scripts/options.awk out=pnglibconf.tf6\
	    logunsupported=1 version=search ${srcdir}/pngconf.h -\
	    ${srcdir}/scripts/pnglibconf.dfa 1>&2
	$(AWK) -f ${srcdir}/scripts/options.awk out=pnglibconf.tf7\
	    pnglibconf.tf6 1>&2
	rm pnglibconf.tf6
	mv pnglibconf.tf7 $@

$(libpng@PNGLIB_MAJOR@@PNGLIB_MINOR@_la_OBJECTS): png.h pngconf.h \
	pnglibconf.h pngpriv.h pngdebug.h pnginfo.h pngstruct.h pngprefix.h

test: check-am

# Extra checks
check: scripts/symbols.chk

# Don't distribute the generated script files
dist-hook:
	cd '$(top_distdir)'; rm -f $(SCRIPT_CLEANFILES)

# Make links between installed files with release-specific names and the generic
# file names.  If this install rule is run the generic names will be deleted and
# recreated - this has obvious issues for systems with multiple installations.

install-header-links:
	@set -ex; cd '$(DESTDIR)$(includedir)'; for f in $(HEADERS); do \
	   rm -f "$$f"; $(LN_S) "$(PNGLIB_BASENAME)/$$f" "$$f"; done

uninstall-header-links:
	cd '$(DESTDIR)$(includedir)'; rm -f $(HEADERS)

install-libpng-pc:
	@set -ex; cd '$(DESTDIR)$(pkgconfigdir)'; rm -f libpng.pc; \
	   $(LN_S) '$(PNGLIB_BASENAME).pc' libpng.pc

uninstall-libpng-pc:
	rm -f '$(DESTDIR)$(pkgconfigdir)/libpng.pc'

install-library-links:
	@set -x; cd '$(DESTDIR)$(libdir)';\
	for ext in $(EXT_LIST); do\
	   rm -f "libpng.$$ext";\
           if test -f "$(PNGLIB_BASENAME).$$ext"; then\
              $(LN_S) "$(PNGLIB_BASENAME).$$ext" "libpng.$$ext" || exit 1;\
           fi;\
	done

uninstall-library-links:
	@set -x; cd '$(DESTDIR)$(libdir)'; for ext in $(EXT_LIST); do\
	   rm -f "libpng.$$ext"; done

install-libpng-config:
	@set -ex; cd '$(DESTDIR)$(bindir)'; rm -f libpng-config; \
	   $(LN_S) '$(PNGLIB_BASENAME)-config' libpng-config

uninstall-libpng-config:
	rm -f '$(DESTDIR)$(bindir)/libpng-config'

# If --enable-unversioned-links is specified the header and lib file links
# will be automatically made on a 'make install':

@DO_INSTALL_LINKS_TRUE@install-data-hook: install-header-links
@DO_INSTALL_LINKS_TRUE@uninstall-hook: uninstall-header-links
@DO_INSTALL_LINKS_TRUE@install-exec-hook: install-library-links
@DO_INSTALL_LINKS_TRUE@uninstall-hook: uninstall-library-links

# Likewise, --install-pc causes libpng.pc to be constructed:

@DO_INSTALL_LIBPNG_PC_TRUE@install-data-hook: install-libpng-pc
@DO_INSTALL_LIBPNG_PC_TRUE@uninstall-hook: uninstall-libpng-pc

# And --install-config:

@DO_INSTALL_LIBPNG_CONFIG_TRUE@install-exec-hook: install-libpng-config
@DO_INSTALL_LIBPNG_CONFIG_TRUE@uninstall-hook: uninstall-libpng-config

# The following addition ensures that 'make all' always builds the test programs
# too.  It used to, but some change either in libpng or configure stopped this
# working.
all-am: $(check_PROGRAMS)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

# Tell GNU make to disable its built-in pattern rules.
%:: %,v
%:: RCS/%,v
%:: RCS/%
%:: s.%
%:: SCCS/s.%
