.TH PNG 5 "July 14, 2025"
.SH NAME
png \- Portable Network Graphics (PNG) format

.SH DESCRIPTION
PNG (Portable Network Graphics) is an extensible file format for the
lossless, portable, well-compressed storage of raster images.  PNG
provides a patent-free replacement for GIF, and can also replace many
common uses of TIFF. Indexed-color, grayscale, and truecolor images
are supported, plus an optional alpha channel.  Sample depths range
from 1 to 16 bits.
.P
PNG is designed to work well in online viewing applications, such
as the World Wide Web, so it is fully streamable with a progressive
display option.  PNG is robust, providing both full file integrity
checking and fast, simple detection of common transmission errors.
Also, PNG can store color space data for improved color matching on
heterogeneous platforms.

.SH "SEE ALSO"
.BR "libpng"(3), " zlib"(3), " deflate"(5), " " and " zlib"(5)
.LP
Portable Network Graphics (PNG) Specification (Third Edition):
.IP
.br
W3C Recommendation 24 June 2025
.br
https://www.w3.org/TR/2025/REC-png-3-20250624/
.LP
Portable Network Graphics (PNG) Specification (Second Edition):
.IP
.br
ISO/IEC 15948:2004(E)
.br
https://www.iso.org/standard/29581.html
.IP
.br
W3C Recommendation 10 November 2003
.br
https://www.w3.org/TR/2003/REC-PNG-20031110/
.LP
Portable Network Graphics (PNG) Specification Version 1.2:
.IP
.br
Published by the PNG Development Group on 14 July 1999
.br
https://www.libpng.org/pub/png/spec/1.2/
.LP
Portable Network Graphics (PNG) Specification Version 1.1:
.IP
.br
Published by the PNG Development Group on 15 February 1999
.br
https://www.libpng.org/pub/png/spec/1.1/
.LP
Portable Network Graphics (PNG) Specification Version 1.0:
.IP
.br
IETF RFC 2083, March 1997
.br
https://www.ietf.org/rfc/rfc2083.txt
.IP
.br
W3C Recommendation 1 October 1996
.br
https://www.w3.org/TR/REC-png-961001

.SH AUTHORS
This man page: Glenn Randers-Pehrson, Cosmin Truta, Chris Lilley.
.LP
PNG Specification (Third Edition): Chris Blume et al.
.LP
PNG Specification (Second Edition): David Duce et al.
.LP
PNG Specification Version 1.2: Glenn Randers-Pehrson et al.
.LP
PNG Specification Version 1.1: Glenn Randers-Pehrson et al.
.LP
PNG Specification Version 1.0: Thomas Boutell et al.

.\" end of man page
