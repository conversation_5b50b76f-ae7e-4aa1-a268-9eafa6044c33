libpng 1.6.51.git
=================

This is a development version, not intended to be a public release.
It will be replaced by a public release, or by another development
version, at a later time.


libpng 1.6.50 - July 1, 2025
============================

This is a public release of libpng, intended for use in production code.


Files available for download
----------------------------

Source files with LF line endings (for Unix/Linux):

 * libpng-1.6.50.tar.xz (LZMA-compressed, recommended)
 * libpng-1.6.50.tar.gz (deflate-compressed)

Source files with CRLF line endings (for Windows):

 * lpng1650.7z (LZMA-compressed, recommended)
 * lpng1650.zip (deflate-compressed)

Other information:

 * README.md
 * LICENSE.md
 * AUTHORS.md
 * TRADEMARK.md


Changes from version 1.6.49 to version 1.6.50
---------------------------------------------

 * Improved the detection of the RVV Extension on the RISC-V platform.
   (Contributed by Filip Wasil)
 * Replaced inline ASM with C intrinsics in the RVV code.
   (Contributed by Filip Wasil)
 * Fixed a decoder defect in which unknown chunks trailing IDAT, set
   to go through the unknown chunk handler, incorrectly triggered
   out-of-place IEND errors.
   (Contributed by John Bowler)
 * Fixed the CMake file for cross-platform builds that require `libm`.


Send comments/corrections/commendations to png-mng-implement at lists.sf.net.
Subscription is required; visit
https://lists.sourceforge.net/lists/listinfo/png-mng-implement
to subscribe.
