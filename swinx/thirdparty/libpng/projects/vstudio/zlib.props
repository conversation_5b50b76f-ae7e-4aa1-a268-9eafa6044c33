<?xml version="1.0" encoding="utf-8"?>
<!--
 * zlib.props - location of zlib source
 *
 * Copyright (c) 2018-2024 Co<PERSON><PERSON>
 * Copyright (c) 1998-2011 <PERSON>
 *
 * This code is released under the libpng license.
 * For conditions of distribution and use, see the disclaimer
 * and license in png.h

 * You may need to edit this file in order to update the location
 * of the zlib source code.
 -->

<Project ToolsVersion="4.0"
         xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <!-- Place the name of the directory containing the source of zlib used for
         debugging in this property.

         The directory need only contain the '.c' and '.h' files from the
         source.

         If you use a relative directory name (as below) then it must be
         relative to the project directories; these are one level deeper than
         the directories containing this file.

         If the version of zlib you use does not match that used when the
         distribution was built you will get warnings from pngtest that the
         zlib versions do not match.  The zlib version used in this build is
         recorded below:
     -->
    <ZLibSrcDir>..\..\..\..\zlib</ZLibSrcDir>

    <!-- The following line allows compilation for an ARM target with Visual
         Studio 2012.  Notice that this is not supported by the Visual Studio
         2012 IDE and that the programs that result cannot be run unless they
         are signed by Microsoft.  This is therefore untested; only Microsoft
         can test it:
     -->
    <WindowsSDKDesktopARMSupport>true</WindowsSDKDesktopARMSupport>

    <!-- The following lines provide a global (solution-level) control of the
         warnings issued by the compiler.

         Considering how different versions of Visual Studio sometimes require
         different settings, and their compilers issue different warnings, we
         set TreatWarningAsError to false to avoid unforeseen and undesirable
         build failures for the users who upgrade to a newer Visual Studio that
         might bring along a more pedantic compiler:
     -->
   <WarningLevel>Level3</WarningLevel>
   <TreatWarningAsError>false</TreatWarningAsError>
  </PropertyGroup>
</Project>
