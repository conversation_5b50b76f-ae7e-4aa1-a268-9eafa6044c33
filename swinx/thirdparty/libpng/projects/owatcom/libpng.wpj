40
projectIdent
0
VpeMain
1
WRect
256
0
8960
9294
2
MProject
3
MCommand
322
# Locations of zlib and (if required) awk (change as required:)
set zlib=..\..\..\zlib
set awk=
#
@if not exist pngconfig.dfa $(MAKE) $(__MAKEOPTS__) -f pngconfig.mak defaults
@if exist config.inf type config.inf
@echo Checking for the libpng configuration file pnglibconf.h
$(MAKE) $(__MAKEOPTS__) -f pngconfig.mak
4
MCommand
19
@type pngconfig.inf
4
5
WFileName
10
libpng.tgt
6
WFileName
11
pngtest.tgt
7
WFileName
12
pngvalid.tgt
8
WFileName
12
pngstest.tgt
9
WVList
4
10
VComponent
11
WRect
0
0
5638
4174
0
0
12
WFileName
10
libpng.tgt
0
0
13
VComponent
14
WRect
1280
1550
5638
4174
0
0
15
WFileName
11
pngtest.tgt
0
1
16
VComponent
17
WRect
524
497
5638
4174
0
0
18
WFileName
12
pngvalid.tgt
0
1
19
VComponent
20
WRect
2054
2701
5674
4232
0
0
21
WFileName
12
pngstest.tgt
0
1
19
