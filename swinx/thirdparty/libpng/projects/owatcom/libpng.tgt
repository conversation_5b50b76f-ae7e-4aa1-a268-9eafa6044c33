40
targetIdent
0
MProject
1
MComponent
0
2
WString
3
LIB
3
WString
5
n_2sn
1
0
0
4
MCommand
0
5
MCommand
0
6
MItem
10
libpng.lib
7
WString
3
LIB
8
WVList
0
9
WVList
1
10
ActionStates
11
WString
5
&Make
12
WVList
0
-1
1
1
0
13
WPickList
16
14
MItem
3
*.c
15
WString
4
COBJ
16
WVList
2
17
MVState
18
WString
3
WCC
19
WString
25
n????Include directories:
1
20
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
21
MVState
22
WString
3
WCC
23
WString
25
n????Include directories:
0
24
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
25
WVList
1
26
ActionStates
27
WString
5
&Make
28
WVList
0
-1
1
1
0
29
MItem
11
..\..\png.c
30
WString
4
COBJ
31
WVList
0
32
WVList
0
14
1
1
0
33
MItem
16
..\..\pngerror.c
34
WString
4
COBJ
35
WVList
0
36
WVList
0
14
1
1
0
37
MItem
14
..\..\pngget.c
38
WString
4
COBJ
39
WVList
0
40
WVList
0
14
1
1
0
41
MItem
14
..\..\pngmem.c
42
WString
4
COBJ
43
WVList
0
44
WVList
0
14
1
1
0
45
MItem
16
..\..\pngpread.c
46
WString
4
COBJ
47
WVList
0
48
WVList
0
14
1
1
0
49
MItem
15
..\..\pngread.c
50
WString
4
COBJ
51
WVList
0
52
WVList
0
14
1
1
0
53
MItem
14
..\..\pngrio.c
54
WString
4
COBJ
55
WVList
0
56
WVList
0
14
1
1
0
57
MItem
16
..\..\pngrtran.c
58
WString
4
COBJ
59
WVList
0
60
WVList
0
14
1
1
0
61
MItem
16
..\..\pngrutil.c
62
WString
4
COBJ
63
WVList
0
64
WVList
0
14
1
1
0
65
MItem
14
..\..\pngset.c
66
WString
4
COBJ
67
WVList
0
68
WVList
0
14
1
1
0
69
MItem
16
..\..\pngtrans.c
70
WString
4
COBJ
71
WVList
0
72
WVList
0
14
1
1
0
73
MItem
14
..\..\pngwio.c
74
WString
4
COBJ
75
WVList
0
76
WVList
0
14
1
1
0
77
MItem
16
..\..\pngwrite.c
78
WString
4
COBJ
79
WVList
0
80
WVList
0
14
1
1
0
81
MItem
16
..\..\pngwtran.c
82
WString
4
COBJ
83
WVList
0
84
WVList
0
14
1
1
0
85
MItem
16
..\..\pngwutil.c
86
WString
4
COBJ
87
WVList
0
88
WVList
0
14
1
1
0
