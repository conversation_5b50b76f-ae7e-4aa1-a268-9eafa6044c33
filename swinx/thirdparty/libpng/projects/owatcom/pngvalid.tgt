40
targetIdent
0
MProject
1
MComponent
0
2
WString
4
NEXE
3
WString
5
nc2en
1
0
0
4
MCommand
0
5
MCommand
8
pngvalid
6
MItem
12
pngvalid.exe
7
WString
4
NEXE
8
WVList
6
9
MVState
10
WString
7
WINLINK
11
WString
11
?????Stack:
1
12
WString
4
768k
0
13
MVState
14
WString
7
WINLINK
15
WString
28
?????Library directories(;):
1
16
WString
8
$(%zlib)
0
17
MVState
18
WString
7
WINLINK
19
WString
18
?????Libraries(,):
1
20
WString
19
libpng.lib zlib.lib
0
21
MVState
22
WString
7
WINLINK
23
WString
11
?????Stack:
0
24
WString
4
768k
0
25
MVState
26
WString
7
WINLINK
27
WString
28
?????Library directories(;):
0
28
WString
8
$(%zlib)
0
29
MVState
30
WString
7
WINLINK
31
WString
18
?????Libraries(,):
0
32
WString
19
libpng.lib zlib.lib
0
33
WVList
0
-1
1
1
0
34
WPickList
2
35
MItem
3
*.c
36
WString
4
COBJ
37
WVList
2
38
MVState
39
WString
3
WCC
40
WString
25
n????Include directories:
1
41
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
42
MVState
43
WString
3
WCC
44
WString
25
n????Include directories:
0
45
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
46
WVList
0
-1
1
1
0
47
MItem
33
..\..\contrib\libtests\pngvalid.c
48
WString
4
COBJ
49
WVList
0
50
WVList
0
35
1
1
0
