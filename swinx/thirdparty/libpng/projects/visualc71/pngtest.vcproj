<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="pngtest"
	RootNamespace="pngtest">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="DLL Release|Win32"
			OutputDirectory=".\Win32_DLL_Release\Test"
			IntermediateDirectory=".\Win32_DLL_Release\Test"
			ConfigurationType="1">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\scripts;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;PNG_DLL;PNG_NO_STDIO;_CRT_SECURE_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				WarningLevel="3"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				Description="Testing..."
				CommandLine="set path=$(OutDir)\..;$(OutDir)\..\ZLib
$(TargetPath) ..\..\pngtest.png $(IntDir)\pngout.png"
				Outputs="$(IntDir)\pngout.png"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/pngtest.exe"
				LinkIncremental="1"
				SubSystem="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="DLL Debug|Win32"
			OutputDirectory=".\Win32_DLL_Debug\Test"
			IntermediateDirectory=".\Win32_DLL_Debug\Test"
			ConfigurationType="1">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\scripts;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;PNG_DLL;PNG_NO_STDIO;_CRT_SECURE_NO_WARNINGS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				Description="Testing..."
				CommandLine="set path=$(OutDir)\..;$(OutDir)\..\ZLib
$(TargetPath) ..\..\pngtest.png $(IntDir)\pngout.png"
				Outputs="$(IntDir)\pngout.png"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/pngtest.exe"
				GenerateDebugInformation="TRUE"
				SubSystem="1"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="DLL VB|Win32"
			OutputDirectory=".\Win32_DLL_VB\Test"
			IntermediateDirectory=".\Win32_DLL_VB\Test"
			ConfigurationType="1">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\scripts;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;PNG_DLL;PNG_NO_STDIO;PNGAPI=__stdcall;PNG_USER_PRIVATEBUILD;_CRT_SECURE_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				WarningLevel="2"
				CallingConvention="2"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				Description="Testing..."
				CommandLine="set path=$(OutDir)\..;$(OutDir)\..\..\Win32_DLL_Release\ZLib
$(TargetPath) ..\..\pngtest.png $(IntDir)\pngout.png"
				Outputs="$(IntDir)\pngout.png"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/pngtest.exe"
				LinkIncremental="1"
				IgnoreDefaultLibraryNames="$(IntDir)\libpng16b.lib"
				SubSystem="1"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="LIB Release|Win32"
			OutputDirectory=".\Win32_LIB_Release\Test"
			IntermediateDirectory=".\Win32_LIB_Release\Test"
			ConfigurationType="1">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\scripts;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;_CRT_SECURE_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				WarningLevel="3"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				Description="Testing..."
				CommandLine="set path=$(OutDir)\..;$(OutDir)\..\ZLib
$(TargetPath) ..\..\pngtest.png $(IntDir)\pngout.png"
				Outputs="$(IntDir)\pngout.png"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/pngtest.exe"
				LinkIncremental="1"
				SubSystem="1"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="LIB Debug|Win32"
			OutputDirectory=".\Win32_LIB_Debug\Test"
			IntermediateDirectory=".\Win32_LIB_Debug\Test"
			ConfigurationType="1">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\scripts;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;_CRT_SECURE_NO_WARNINGS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				WarningLevel="3"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				Description="Testing..."
				CommandLine="set path=$(OutDir)\..;$(OutDir)\..\ZLib
$(TargetPath) ..\..\pngtest.png $(IntDir)\pngout.png"
				Outputs="$(IntDir)\pngout.png"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/pngtest.exe"
				GenerateDebugInformation="TRUE"
				SubSystem="1"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat">
			<File
				RelativePath="..\..\pngtest.c">
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
