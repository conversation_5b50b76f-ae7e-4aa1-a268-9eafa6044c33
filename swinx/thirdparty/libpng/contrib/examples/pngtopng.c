/*- pngtopng
 *
 * COPYRIGHT: Written by <PERSON>, 2011, 2017.
 * To the extent possible under law, the author has waived all copyright and
 * related or neighboring rights to this work.  This work is published from:
 * United States.
 *
 * Read a PNG and write it out in a fixed format, using the 'simplified API'
 * that was introduced in libpng-1.6.0.
 *
 * This sample code is just the code from 'example.c' with some error handling
 * added.  See example.c in the top-level libpng directory for more comments.
 */
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

/* Normally use <png.h> here to get the installed libpng, but this is done to
 * ensure the code picks up the local libpng implementation:
 */
#include "../../png.h"

#if !defined(PNG_SIMPLIFIED_READ_SUPPORTED) || \
    !defined(PNG_SIMPLIFIED_WRITE_SUPPORTED)
#error This program requires libpng supporting the simplified read/write API
#endif


int
main(int argc, const char **argv)
{
   int result = 1;

   if (argc == 3)
   {
      png_image image;

      /* Only the image structure version number needs to be set. */
      memset(&image, 0, sizeof image);
      image.version = PNG_IMAGE_VERSION;

      if (png_image_begin_read_from_file(&image, argv[1]))
      {
         png_bytep buffer;

         /* Change this to try different formats!  If you set a colormap format
          * then you must also supply a colormap below.
          */
         image.format = PNG_FORMAT_RGBA;

         buffer = malloc(PNG_IMAGE_SIZE(image));

         if (buffer != NULL)
         {
            if (png_image_finish_read(&image, NULL /*background*/, buffer,
                                      0 /*row_stride*/, NULL /*colormap */))
            {
               if (png_image_write_to_file(
                      &image, argv[2], 0 /*convert_to_8bit*/, buffer,
                      0 /*row_stride*/, NULL /*colormap*/))
                  result = 0;

               else
                  fprintf(stderr, "pngtopng: write %s: %s\n", argv[2],
                          image.message);
            }

            else
               fprintf(stderr, "pngtopng: read %s: %s\n", argv[1],
                       image.message);

            free(buffer);
         }

         else
         {
            fprintf(stderr, "pngtopng: out of memory: %lu bytes\n",
                    (unsigned long)PNG_IMAGE_SIZE(image));

            /* This is the only place where a 'free' is required; libpng does
             * the cleanup on error and success, but in this case we couldn't
             * complete the read because of running out of memory and so libpng
             * has not got to the point where it can do cleanup.
             */
            png_image_free(&image);
         }
      }

      else
         /* Failed to read the first argument: */
         fprintf(stderr, "pngtopng: %s: %s\n", argv[1], image.message);
   }

   else
      /* Wrong number of arguments */
      fprintf(stderr, "pngtopng: usage: pngtopng input-file output-file\n");

   return result;
}
