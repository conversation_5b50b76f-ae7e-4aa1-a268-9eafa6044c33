pngsuite
--------
Copyright (c) <PERSON>, 1999, 2011, 2012
Two images (ftbbn0g01.png and ftbbn0g02.png) are by <PERSON>, 2012

Permission to use, copy, modify, and distribute these images for any
purpose and without fee is hereby granted.

The 15 "bas*.png" images are part of the much larger PngSuite test-set of
images, available for developers of PNG supporting software. The
complete set, available at http:/www.schaik.com/pngsuite/, contains
a variety of images to test interlacing, gamma settings, ancillary
chunks, etc.

The "ft*.png" images are "free/libre" replacements for the transparent
corresponding t*.png images in the PngSuite.

The "i*.png" images are the same images, but interlaced.

The images in this directory represent the basic PNG color-types:
grayscale (1-16 bit deep), full color (8 or 16 bit), paletted
(1-8 bit) and grayscale or color images with alpha channel. You
can use them to test the proper functioning of PNG software.

    filename       depth type
    ------------ ------ --------------
    basn0g01.png   1-bit grayscale
    basn0g02.png   2-bit grayscale
    basn0g04.png   4-bit grayscale
    basn0g08.png   8-bit grayscale
    basn0g16.png  16-bit grayscale
    basn2c08.png   8-bit truecolor
    basn2c16.png  16-bit truecolor
    basn3p01.png   1-bit paletted
    basn3p02.png   2-bit paletted
    basn3p04.png   4-bit paletted
    basn3p08.png   8-bit paletted
    basn4a08.png   8-bit gray with alpha
    basn4a16.png  16-bit gray with alpha
    basn6a08.png   8-bit RGBA
    basn6a16.png  16-bit RGBA

    ftbbn0g01.png  1-bit grayscale, black bKGD
    ftbbn0g02.png  2-bit grayscale, black bKGD
    ftbbn0g04.png  4-bit grayscale, black bKGD
    ftbbn2c16.png 16-bit truecolor, black bKGD
    ftbbn3p08.png  8-bit paletted, black bKGD
    ftbgn2c16.png 16-bit truecolor, gray bKGD
    ftbgn3p08.png  8-bit paletted, gray bKGD
    ftbrn2c08.png  8-bit truecolor, red bKGD
    ftbwn0g16.png 16-bit gray, white bKGD
    ftbwn3p08.png  8-bit paletted, white bKGD
    ftbyn3p08.png  8-bit paletted, yellow bKGD
    ftp0n0g08.png  8-bit grayscale, opaque
    ftp0n2c08.png  8-bit truecolor, opaque
    ftp0n3p08.png  8-bit paletted, opaque
    ftp1n3p08.png  8-bit paletted, no bKGD

Here is the correct result of typing "pngtest -m bas*.png" in
this directory:

Testing basn0g01.png: PASS (524 zero samples)
 Filter 0 was used 32 times
Testing basn0g02.png: PASS (448 zero samples)
 Filter 0 was used 32 times
Testing basn0g04.png: PASS (520 zero samples)
 Filter 0 was used 32 times
Testing basn0g08.png: PASS (3 zero samples)
 Filter 1 was used 9 times
 Filter 4 was used 23 times
Testing basn0g16.png: PASS (1 zero samples)
 Filter 1 was used 1 times
 Filter 2 was used 31 times
Testing basn2c08.png: PASS (6 zero samples)
 Filter 1 was used 5 times
 Filter 4 was used 27 times
Testing basn2c16.png: PASS (592 zero samples)
 Filter 1 was used 1 times
 Filter 4 was used 31 times
Testing basn3p01.png: PASS (512 zero samples)
 Filter 0 was used 32 times
Testing basn3p02.png: PASS (448 zero samples)
 Filter 0 was used 32 times
Testing basn3p04.png: PASS (544 zero samples)
 Filter 0 was used 32 times
Testing basn3p08.png: PASS (4 zero samples)
 Filter 0 was used 32 times
Testing basn4a08.png: PASS (32 zero samples)
 Filter 1 was used 1 times
 Filter 4 was used 31 times
Testing basn4a16.png: PASS (64 zero samples)
 Filter 0 was used 1 times
 Filter 1 was used 2 times
 Filter 2 was used 1 times
 Filter 4 was used 28 times
Testing basn6a08.png: PASS (160 zero samples)
 Filter 1 was used 1 times
 Filter 4 was used 31 times
Testing basn6a16.png: PASS (1072 zero samples)
 Filter 1 was used 4 times
 Filter 4 was used 28 times
libpng passes test

Willem van Schaik
<willem at schaik.com>
October 1999
