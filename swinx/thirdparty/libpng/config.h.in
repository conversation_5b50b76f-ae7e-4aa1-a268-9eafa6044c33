/* config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H

/* Define to 1 if you have the 'feenableexcept' function. */
#undef HAVE_FEENABLEEXCEPT

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the 'm' library (-lm). */
#undef HAVE_LIBM

/* Define to 1 if you have the 'z' library (-lz). */
#undef HAVE_LIBZ

/* Define to 1 if you have the 'pow' function. */
#undef HAVE_POW

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdio.h> header file. */
#undef HAVE_STDIO_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#undef LT_OBJDIR

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* Turn on ARM Neon optimizations at run-time */
#undef PNG_ARM_NEON_API_SUPPORTED

/* Check for ARM Neon support at run-time */
#undef PNG_ARM_NEON_CHECK_SUPPORTED

/* Enable ARM Neon optimizations */
#undef PNG_ARM_NEON_OPT

/* Enable Intel SSE optimizations */
#undef PNG_INTEL_SSE_OPT

/* Enable LOONGARCH LSX optimizations */
#undef PNG_LOONGARCH_LSX_OPT

/* Turn on MIPS MMI optimizations at run-time */
#undef PNG_MIPS_MMI_API_SUPPORTED

/* Check for MIPS MMI support at run-time */
#undef PNG_MIPS_MMI_CHECK_SUPPORTED

/* Enable MIPS MMI optimizations */
#undef PNG_MIPS_MMI_OPT

/* Turn on MIPS MSA optimizations at run-time */
#undef PNG_MIPS_MSA_API_SUPPORTED

/* Check for MIPS MSA support at run-time */
#undef PNG_MIPS_MSA_CHECK_SUPPORTED

/* Enable MIPS MSA optimizations */
#undef PNG_MIPS_MSA_OPT

/* Turn on POWERPC VSX optimizations at run-time */
#undef PNG_POWERPC_VSX_API_SUPPORTED

/* Check for POWERPC VSX support at run-time */
#undef PNG_POWERPC_VSX_CHECK_SUPPORTED

/* Enable POWERPC VSX optimizations */
#undef PNG_POWERPC_VSX_OPT

/* Enable RISCV RVV optimizations */
#undef PNG_RISCV_RVV_OPT

/* Define to 1 if all of the C89 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
#undef STDC_HEADERS

/* Define to 1 if your <sys/time.h> declares 'struct tm'. */
#undef TM_IN_SYS_TIME

/* Version number of package */
#undef VERSION

/* Define to the equivalent of the C99 'restrict' keyword, or to
   nothing if this is not supported.  Do not define if restrict is
   supported only directly.  */
#undef restrict
/* Work around a bug in older versions of Sun C++, which did not
   #define __restrict__ or support _Restrict or __restrict__
   even though the corresponding Sun C compiler ended up with
   "#define restrict _Restrict" or "#define restrict __restrict__"
   in the previous line.  This workaround can be removed once
   we assume Oracle Developer Studio 12.5 (2016) or later.  */
#if defined __SUNPRO_CC && !defined __RESTRICT && !defined __restrict__
# define _Restrict
# define __restrict__
#endif
