# makefile for libpng on BeOS x86 ELF with gcc
# modified from makefile.linux by <PERSON>er <PERSON>oks
# Copyright (C) 2020-2025 Cosmin Truta
# Copyright (C) 2002, 2006, 2008, 2010-2014 <PERSON>-<PERSON><PERSON>
# Copyright (C) 1999 <PERSON>
# Copyright (C) 1996, 1997 <PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Library name:
LIBNAME=libpng16
PNGMAJ=16

# Shared library names:
LIBSO=$(LIBNAME).so
LIBSOMAJ=$(LIBNAME).so.$(PNGMAJ)

# Utilities:
CC=gcc
AR=ar
RANLIB=ranlib
LN_SF=ln -sf
CP=cp
RM_F=/bin/rm -f

# Where the zlib library and include files are located
ZLIBLIB=/usr/local/lib
ZLIBINC=/usr/local/include

ALIGN=
# For i386:
# ALIGN=-malign-loops=2 -malign-functions=2

# On BeOS, -O1 is actually better than -O3.  This is a known bug but it's
# still here in R4.5
CPPFLAGS=-I$(ZLIBINC) # -DPNG_DEBUG=5
CFLAGS=-O1 -funroll-loops $(ALIGN) # -g
ARFLAGS=rc
# LDFLAGS=-L. -Wl,-rpath,. -L$(ZLIBLIB) -Wl,-rpath,$(ZLIBLIB) -lpng -lz
LDFLAGS=-L. -Wl,-soname=$(LIBSOMAJ) -L$(ZLIBLIB) -lz # -g

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

OBJSDLL = $(OBJS)

.SUFFIXES:      .c .o

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

all: libpng.a $(LIBSO) pngtest

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)
	cp $(LIBSO)* /boot/home/<USER>/lib

$(LIBSOMAJ): $(OBJSDLL)
	$(CC) -nostart -Wl,-soname,$(LIBSOMAJ) \
	 -o $(LIBSOMAJ) $(OBJSDLL) $(LDFLAGS)

pngtest: pngtest.o $(LIBSO)
	$(CC) -L$(ZLIBLIB) -L. -lz -lpng16 -o pngtest pngtest.o

test: pngtest
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-static:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-shared:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) *.o libpng.a pngtest pngout.png
	$(RM_F) $(LIBSO) $(LIBSOMAJ)* pnglibconf.h

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o      png.pic.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o pngerror.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o   pngget.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o   pngmem.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o pngpread.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o  pngread.pic.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o   pngrio.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o pngrtran.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o pngrutil.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o   pngset.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o pngtrans.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o   pngwio.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o pngwrite.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o pngwtran.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o pngwutil.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o: png.h pngconf.h pnglibconf.h
