# makefile for libpng.a and libpng16.so, SGI IRIX with 'cc'
# Copyright (C) 2018-2025 Cosmin Truta
# Copyright (C) 2001-2002, 2006, 2007, 2010-2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Library name:
LIBNAME=libpng16
PNGMAJ=16

# Shared library names:
LIBSO=$(LIBNAME).so
LIBSOMAJ=$(LIBNAME).so.$(PNGMAJ)

# Utilities:
CC=cc
AR=ar
RANLIB=echo
LN_SF=ln -sf
CP=cp
RM_F=/bin/rm -f

# Where the zlib library and include files are located
#ZLIBLIB=/usr/local/lib32
#ZLIBINC=/usr/local/include
#ZLIBLIB=/usr/local/lib
#ZLIBINC=/usr/local/include
ZLIBLIB=../zlib
ZLIBINC=../zlib

# ABI can be blank to use default for your system, -32, -o32, -n32, or -64
# See "man abi".  zlib must be built with the same ABI.
ABI=

# Note: -KPIC is the default anyhow
CPPFLAGS=-I$(ZLIBINC) # -DPNG_DEBUG=5
#CFLAGS=$(ABI) -O -fullwarn -KPIC # -g
CFLAGS=$(ABI) -O -fullwarn # -g
ARFLAGS=rc
LDFLAGS_A=$(ABI) -L. -L$(ZLIBLIB) -lpng16 -lz -lm # -g
LDFLAGS=$(ABI) -L. -L$(ZLIBLIB) -lpng -lz -lm # -g
LDSHARED=cc $(ABI) -shared -soname $(LIBSOMAJ) \
	-set_version sgi$(PNGMAJ).0
# See "man dso" for info about shared objects

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

all: libpng.a pngtest shared

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)

$(LIBSOMAJ): $(OBJS)
	$(LDSHARED) -o $@ $(OBJS)
	$(RM_F) $(LIBSO) $(LIBSOMAJ)

pngtest: pngtest.o libpng.a
	$(CC) -o pngtest $(CFLAGS) pngtest.o $(LDFLAGS)

test: pngtest
	echo
	echo Testing local static library.
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-static:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-shared:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) *.o libpng.a pngtest pngout.png
	$(RM_F) $(LIBSO) $(LIBSOMAJ)* so_locations pnglibconf.h

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
