# makefile for libpng on win-arm64
# Copyright (C) 2021 G<PERSON><PERSON>
# Copyright (C) 1998 <PERSON>
# Copyright (C) 2006,2009,2011,2014 <PERSON><PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h
#
# Assumes that zlib.lib, zconf.h, and zlib.h have been copied to ..\zlib
# To use, do "nmake /f scripts\makefile.vcwin-arm64"

# -------- Microsoft Visual C++ 2.0 and later --------

# Compiler, linker, librarian and other tools
CC = cl
LD = link
AR = lib
CPPFLAGS = -I..\zlib
CFLAGS  = -nologo -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -DPNG_ARM_NEON_OPT=1 -MD -O2 -W3
LDFLAGS = -nologo
ARFLAGS = -nologo
CP = copy
RM = del

# Uncomment next to put error messages in a file
#ERRFILE= >> pngerrs.log

# Pre-built configuration
# See scripts\pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts\pnglibconf.h.prebuilt

# File extensions
O = .obj

# File lists
OBJS1 = png$(O) pngerror$(O) pngget$(O) pngmem$(O) pngpread$(O)
OBJS2 = pngread$(O) pngrio$(O) pngrtran$(O) pngrutil$(O) pngset$(O)
OBJS3 = pngtrans$(O) pngwio$(O) pngwrite$(O) pngwtran$(O) pngwutil$(O)
OBJS4 = arm_init$(O) filter_neon_intrinsics$(O) palette_neon_intrinsics$(O)
OBJS  = $(OBJS1) $(OBJS2) $(OBJS3) $(OBJS4)

# Targets
all: libpng.lib

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

png$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngerror$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngget$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngmem$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngpread$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngread$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngrio$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngrtran$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngrutil$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngset$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngtrans$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngwio$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngwrite$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngwtran$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

pngwutil$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

arm_init$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) arm\$*.c $(ERRFILE)

filter_neon_intrinsics$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) arm\$*.c $(ERRFILE)

palette_neon_intrinsics$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) arm\$*.c $(ERRFILE)

libpng.lib: $(OBJS)
	-$(RM) $@
	$(AR) $(ARFLAGS) -out:$@ $(OBJS) $(ERRFILE)

pngtest.exe: pngtest$(O) libpng.lib
	$(LD) $(LDFLAGS) -out:$@ pngtest$(O) libpng.lib ..\zlib\zlib.lib $(ERRFILE)

pngtest$(O): png.h pngconf.h pnglibconf.h
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c $(ERRFILE)

test: pngtest.exe
	pngtest.exe

clean:
	-$(RM) *$(O)
	-$(RM) libpng.lib
	-$(RM) pnglibconf.h
	-$(RM) pngtest.exe
	-$(RM) pngout.png

# End of makefile for libpng
