# Commodore Amiga Makefile
# makefile for libpng and SAS C V6.5x compiler
# Copyright (C) 1995-2000 <PERSON> Faust
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h
#
# Location/path of zlib include files
ZLIB=/zlib
#compiler
CC=sc
#compiler flags
# WARNING: a bug in V6.51 causes bad code with OPTGO
#          So use V6.55 or set NOOPTGO!!!!!!!!!
CFLAGS= NOSTKCHK PARMS=REG OPTIMIZE OPTGO OPTPEEP OPTINLOCAL OPTINL\
	OPTLOOP OPTRDEP=4 OPTDEP=4 OPTCOMP=4 INCLUDEDIR=$(ZLIB) \
	DEFINE=PNG_INTERNAL
#linker flags
LDFLAGS= SD ND BATCH
#link libs
LDLIBS= libpng.lib libgz.lib LIB:scm.lib LIB:sc.lib Lib:amiga.lib
# linker
LN= slink
# file deletion command
RM= delete quiet
# library (.lib) file creation command
AR= oml

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

all: libpng.lib pngtest

libpng.lib: $(OBJS)
-$(RM) libpng.lib
$(AR) libpng.lib r $(OBJS)

$(OBJS): pngpriv.h png.h pngconf.h pnglibconf.h pnginfo.h pngstruct.h pngdebug.h

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
COPY $(PNGLIBCONF_H_PREBUILT) TO pnglibconf.h

pngtest: pngtest.o libpng.lib
$(LN) <WITH <
$(LDFLAGS)
TO pngtest
FROM LIB:c.o pngtest.o
LIB $(LDLIBS)
<

