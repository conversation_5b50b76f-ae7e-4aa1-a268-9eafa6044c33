# makefile for libpng
# Copyright (C) 2022-2025 <PERSON><PERSON>in <PERSON>
# Copyright (C) 2002, 2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h
#
# Modified for LC56/ATARI assumes libz.lib is in same dir and uses default
# rules for library management
#
CPPFLAGS = -I../zlib
CFLAGS = -O
LBR = png.lib
LDFLAGS = -L. -L../zlib -lpng -lz -lm

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = $(LBR)(png.o) $(LBR)(pngerror.o) $(LBR)(pngget.o) $(LBR)(pngmem.o) \
       $(LBR)(pngpread.o) $(LBR)(pngread.o) $(LBR)(pngrio.o) \
       $(LBR)(pngrtran.o) $(LBR)(pngrutil.o) $(LBR)(pngset.o) \
       $(LBR)(pngtrans.o) $(LBR)(pngwio.o) $(LBR)(pngwrite.o) \
       $(LBR)(pngwtran.o) $(LBR)(pngwutil.o)

all: $(LBR) pngtest.ttp

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $*.c

$(LBR): $(OBJS)

$(OBJS): pngpriv.h png.h pngconf.h pnglibconf.h pnginfo.h pngstruct.h pngdebug.h

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	cp $(PNGLIBCONF_H_PREBUILT) $@

pngtest.ttp: pngtest.o $(LBR)
	$(CC) $(CFLAGS) $(LDFLAGS) -o$@ pngtest.o

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false
