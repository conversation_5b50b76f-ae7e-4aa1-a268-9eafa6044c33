# makefile for libpng, HPUX (10.20 and 11.00) using the ANSI/C product.
# Copyright (C) 2018-2025 Cosmin Truta
# Copyright (C) 1999-2002, 2006, 2009, 2010-2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42
# Contributed by <PERSON> and updated by <PERSON>, Hewlett Packard
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Where the zlib library and include files are located
ZLIBLIB=/opt/zlib/lib
ZLIBINC=/opt/zlib/include

# Note that if you plan to build a libpng shared library, zlib must also
# be a shared library, which zlib's configure does not do.  After running
# zlib's configure, edit the appropriate lines of makefile to read:
#   CFLAGS=-O1 -DHAVE_UNISTD -DUSE_MAP -fPIC \
#   LDSHARED=ld -b
#   SHAREDLIB=libz.sl

# Library name:
LIBNAME=libpng16
PNGMAJ=16

# Shared library names:
LIBSO=$(LIBNAME).sl
LIBSOMAJ=$(LIBNAME).sl.$(PNGMAJ)

# Utilities:
CC=cc
AR=ar
RANLIB=ranlib
LN_SF=ln -sf
CP=cp
RM_F=/bin/rm -f

CPPFLAGS=-I$(ZLIBINC) \
        -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64 -DHAVE_UNISTD_H -DUSE_MMAP
CFLAGS=-O -Ae -Wl,+vnocompatwarnings +DD64 +Z
# Caution: be sure you have built zlib with the same CFLAGS.
CCFLAGS=-O -Ae -Wl,+vnocompatwarnings +DD64 +Z
ARFLAGS=rc
LDFLAGS=-L. -L$(ZLIBLIB) -lpng -lz -lm

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

OBJSDLL = $(OBJS:.o=.pic.o)

.SUFFIXES:	.c .o .pic.o

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

.c.pic.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) +z -o $@ $*.c

all: libpng.a $(LIBSO) pngtest

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)

$(LIBSOMAJ): $(OBJSDLL)
	$(LD) -b +s \
	+h $(LIBSOMAJ) -o $(LIBSOMAJ) $(OBJSDLL)

pngtest: pngtest.o libpng.a
	$(CC) -o pngtest $(CCFLAGS) pngtest.o $(LDFLAGS)

test: pngtest
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-static:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-shared:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) *.o libpng.a pngtest pngout.png
	$(RM_F) $(LIBSO) $(LIBSOMAJ)* pnglibconf.h

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
