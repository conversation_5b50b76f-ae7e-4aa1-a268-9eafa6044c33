# makefile for libpng under FreeBSD
# Copyright (C) 2018-2025 Cosmin Tru<PERSON>
# Copyright (C) 2014 <PERSON> and <PERSON><PERSON>
# Copyright (C) 2002, 2007, 2009 <PERSON> and <PERSON><PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

LIB=		png
SHLIB_VER=	16
SHLIB_MAJOR=	${SHLIB_VER}
SHLIB_MINOR=	0

ZLIBLIB=	/usr/lib
ZLIBINC=	/usr/include

LDADD+=		-lm -lz
#LDADD+=	-lm -lz -lssp_nonshared   # for OSVERSION < 800000 ?

DPADD+=		${LIBM} ${LIBZ}

CPPFLAGS+=	-I. -I${ZLIBINC}
CFLAGS+=	-Wall -Wextra -Wundef
LDFLAGS+=	-L. -L${ZLIBLIB}

CLEANFILES+=	pngtest pngtest.o pngout.png

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT= scripts/pnglibconf.h.prebuilt

SRCS=	png.c pngerror.c pngget.c pngmem.c pngpread.c \
	pngread.c pngrio.c pngrtran.c pngrutil.c pngset.c \
	pngtrans.c pngwio.c pngwrite.c pngwtran.c pngwutil.c

.c.o:
	${CC} -c ${CPPFLAGS} ${CFLAGS} -o $@ $*.c

pnglibconf.h:	${PNGLIBCONF_H_PREBUILT}
	cp ${PNGLIBCONF_H_PREBUILT} $@

pngtest:	pngtest.o libpng.a
	${CC} ${LDFLAGS} -static -o pngtest pngtest.o -lpng ${LDADD}

test:	pngtest
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

.include <bsd.lib.mk>
