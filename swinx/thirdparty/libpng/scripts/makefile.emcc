# makefile for libpng using emscripten
# Copyright (C) 2000, 2014, 2019-2025 Cosmin Truta
# Copyright (C) 2021 <PERSON>
# Copyright (C) 2008, 2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Location of the zlib library and include files
ZLIBINC = ../zlib
ZLIBLIB = ../zlib

# Compiler, linker, lib and other tools
CC = emcc
LD = $(CC)
AR = emar
RANLIB = emranlib
CP = cp
RM_F = rm -f

STDC = -pedantic-errors # -std=c99
WARN = -Wall -Wextra -Wundef
WARNMORE = -Wcast-align -Wconversion -Wshadow -Wpointer-arith -Wwrite-strings \
           -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes
LOCAL_CPPFLAGS =
CPPFLAGS = -I$(ZLIBINC) # -DPNG_DEBUG=5
ALL_CPPFLAGS = $(LOCAL_CPPFLAGS) $(CPPFLAGS)
LOCAL_CFLAGS = $(STDC) $(WARN) # $(WARNMORE)
CFLAGS = -O2
ALL_CFLAGS = $(LOCAL_CFLAGS) $(CFLAGS)
ARFLAGS = rc
LDFLAGS = -L$(ZLIBLIB)
PNGTEST_LDFLAGS = --preload-file=pngtest.png
LIBS = -lz -lm

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

# File lists
OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

# Targets
all: static

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

.c.o:
	$(CC) -c $(ALL_CPPFLAGS) $(ALL_CFLAGS) -o $@ $*.c

static: libpng.a pngtest

shared:
	@echo This makefile cannot create shared libraries.
	@false

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

test: pngtest
	node ./pngtest

pngtest: pngtest.o libpng.a
	$(LD) $(LDFLAGS) $(PNGTEST_LDFLAGS) -o $@ pngtest.o libpng.a $(LIBS)

clean:
	$(RM_F) *.o libpng.a pngtest pngout.png pnglibconf.h
	$(RM_F) pngtest.data pngtest.wasm

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
