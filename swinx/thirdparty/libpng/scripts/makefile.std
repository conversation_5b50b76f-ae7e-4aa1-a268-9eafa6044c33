# makefile for libpng
# Copyright (C) 2015, 2018-2025 Co<PERSON>in <PERSON>
# Copyright (C) 2002, 2006, 2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Where the zlib library and include files are located
#ZLIBLIB=/usr/local/lib
#ZLIBINC=/usr/local/include
ZLIBLIB=../zlib
ZLIBINC=../zlib

CC = cc
CPP = $(CC) -E
LD = $(CC)
AR = ar
RANLIB = ranlib
MV_F = mv -f
RM_F = rm -f
AWK = awk

NOHWOPT = -DPNG_ARM_NEON_OPT=0 -DPNG_MIPS_MSA_OPT=0 \
          -DPNG_POWERPC_VSX_OPT=0 -DPNG_INTEL_SSE_OPT=0 \
          -DPNG_RISCV_RVV_OPT=0
DFNFLAGS = # DFNFLAGS contains -D options to use in the libpng build
DFA_EXTRA = # extra files that can be used to control configuration
CPPFLAGS = -I$(ZLIBINC) $(NOHWOPT) # -DPNG_DEBUG=5
CFLAGS = -O # -g
ARFLAGS = rc
LDFLAGS = -L$(ZLIBLIB) # -g
LIBS = -lz -lm

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

all: libpng.a pngtest

# The standard pnglibconf.h exists as scripts/pnglibconf.h.prebuilt,
# copy this if the following doesn't work.
pnglibconf.h: pnglibconf.dfn
	$(RM_F) $@ pnglibconf.c pnglibconf.out pnglibconf.tmp
	echo '#include "pnglibconf.dfn"' >pnglibconf.c
	@echo "## If '$(CC) -E' fails, try /lib/cpp (e.g. CPP='/lib/cpp')" >&2
	$(CPP) $(DFNFLAGS) pnglibconf.c >pnglibconf.out
	$(AWK) -f scripts/dfn.awk out=pnglibconf.tmp pnglibconf.out >&2
	$(MV_F) pnglibconf.tmp $@

pnglibconf.dfn: scripts/pnglibconf.dfa scripts/options.awk pngconf.h pngusr.dfa $(DFA_XTRA)
	$(RM_F) $@ pnglibconf.pre pnglibconf.tmp
	@echo "## Calling $(AWK) from scripts/pnglibconf.mak" >&2
	@echo "## If 'awk' fails, try a better awk (e.g. AWK='nawk')" >&2
	$(AWK) -f scripts/options.awk out=pnglibconf.pre\
	    version=search pngconf.h scripts/pnglibconf.dfa\
	    pngusr.dfa $(DFA_XTRA) >&2
	$(AWK) -f scripts/options.awk out=pnglibconf.tmp pnglibconf.pre >&2
	$(MV_F) pnglibconf.tmp $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

pngtest: pngtest.o libpng.a
	$(LD) $(LDFLAGS) -o $@ pngtest.o libpng.a $(LIBS)

test: pngtest
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) *.o libpng.a pngtest pngout.png pnglibconf.h
	$(RM_F) pnglibconf.c pnglibconf.dfn pnglibconf.out pnglibconf.pre

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
