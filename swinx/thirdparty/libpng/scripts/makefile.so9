# makefile for libpng on Solaris 9 (beta) with <PERSON>e cc
# Updated by <PERSON> for Solaris 9
# Contributed by <PERSON>, based on makefile.linux
# Copyright (C) 2018-2025 Cosmin Truta
# Copyright (C) 2002, 2006, 2008, 2010-2014 <PERSON><PERSON>
# Copyright (C) 1998-2001 <PERSON>
# Copyright (C) 1996-1997 <PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Library name:
LIBNAME=libpng16
PNGMAJ=16

# Shared library names:
LIBSO=$(LIBNAME).so
LIBSOMAJ=$(LIBNAME).so.$(PNGMAJ)

# Utilities:
CC=cc
AR=ar
RANLIB=echo
LN_SF=ln -f -s
CP=cp
RM_F=/bin/rm -f

# Where the zlib library and include files are located
# Changing these to ../zlib poses a security risk.  If you want
# to have zlib in an adjacent directory, specify the full path instead of "..".
#ZLIBLIB=../zlib
#ZLIBINC=../zlib
#ZLIBLIB=/usr/local/lib
#ZLIBINC=/usr/local/include
#Use the preinstalled zlib that comes with Solaris 9:
ZLIBLIB=/usr/lib
ZLIBINC=/usr/include

CPPFLAGS=-I$(ZLIBINC) # -DPNG_DEBUG=5
CFLAGS=-O3
ARFLAGS=rc
LDFLAGS=-L. -R. -L$(ZLIBLIB) -R$(ZLIBLIB) -lpng16 -lz -lm

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

OBJSDLL = $(OBJS:.o=.pic.o)

.SUFFIXES:      .c .o .pic.o

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

.c.pic.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -KPIC -o $@ $*.c

all: libpng.a $(LIBSO) pngtest

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)

$(LIBSOMAJ): $(OBJSDLL)
	@case "`type ld`" in *ucb*) \
	echo; \
	echo '## WARNING:'; \
	echo '## The commands "CC" and "LD" must NOT refer to /usr/ucb/cc'; \
	echo '## and /usr/ucb/ld.  If they do, you need to adjust your PATH'; \
	echo '## environment variable to put /usr/ccs/bin ahead of /usr/ucb.'; \
	echo '## The environment variable LD_LIBRARY_PATH should not be set'; \
	echo '## at all.  If it is, things are likely to break because of'; \
	echo '## the libucb dependency that is created.'; \
	echo; \
	;; \
	esac
	$(LD) -G -h $(LIBSOMAJ) \
	 -o $(LIBSOMAJ) $(OBJSDLL)

pngtest: pngtest.o $(LIBSO)
	$(CC) -o pngtest $(CFLAGS) pngtest.o $(LDFLAGS)

test: pngtest
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-static:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-shared:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) *.o libpng.a pngtest pngout.png
	$(RM_F) $(LIBSO) $(LIBSOMAJ)* pnglibconf.h

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o      png.pic.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o pngerror.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o   pngget.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o   pngmem.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o pngpread.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o  pngread.pic.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o   pngrio.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o pngrtran.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o pngrutil.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o   pngset.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o pngtrans.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o   pngwio.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o pngwrite.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o pngwtran.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o pngwutil.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o: png.h pngconf.h pnglibconf.h
