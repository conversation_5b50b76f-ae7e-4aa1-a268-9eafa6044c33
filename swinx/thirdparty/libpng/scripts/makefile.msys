# makefile for libpng using MSYS/gcc (shared, static library)
# Copyright (C) 2000, 2019-2025 Cosmin Truta
# Copyright (C) 2012 <PERSON> and <PERSON>
#
# Portions taken from makefile.linux and makefile.gcc:
# Copyright (C) 2000 Cosmin Truta
# Copyright (C) 1998, 1999, 2002, 2006, 2008, 2010-2014 <PERSON> and
# <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1996, 1997 <PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Library name:
LIBNAME=libpng16
PNGMAJ=16

# Shared library names:
LIBSO=$(LIBNAME).dll
LIBSOMAJ=$(LIBNAME).dll.$(PNGMAJ)

# Compiler, linker, lib and other tools
CC = gcc
LD = $(CC)
AR = ar
RANLIB = ranlib
CP = cp
RM_F = rm -f
LN_SF = ln -sf

# Compiler and linker flags
NOHWOPT = -DPNG_ARM_NEON_OPT=0 -DPNG_MIPS_MSA_OPT=0 \
          -DPNG_POWERPC_VSX_OPT=0 -DPNG_INTEL_SSE_OPT=0 \
          -DPNG_RISCV_RVV_OPT=0
STDC = -pedantic-errors
WARN = -Wall -Wextra -Wundef
WARNMORE = -Wcast-align -Wconversion -Wshadow -Wpointer-arith -Wwrite-strings \
           -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes
LOCAL_CPPFLAGS = $(NOHWOPT)
CPPFLAGS = # -DPNG_DEBUG=5
ALL_CPPFLAGS = $(LOCAL_CPPFLAGS) $(CPPFLAGS)
LOCAL_CFLAGS = $(STDC) $(WARN) # $(WARNMORE)
CFLAGS = -O2 # -g
ALL_CFLAGS = $(LOCAL_CFLAGS) $(CFLAGS)
ARFLAGS = rc
LDFLAGS = # -g
LIBS = -lz -lm

# File extensions
EXEEXT = .exe

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

# File lists
OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

# Targets
all: static shared

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

.c.o:
	$(CC) -c $(ALL_CPPFLAGS) $(ALL_CFLAGS) -o $@ $*.c

static: libpng.a pngtest$(EXEEXT)

shared: $(LIBSOMAJ)
	$(CC) -shared -Wl,-soname,$(LIBSOMAJ) -o $(LIBSO)

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)

$(LIBSOMAJ):
	$(CC) -shared -Wl,-soname,$(LIBSOMAJ) -o $(LIBSOMAJ)

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-static:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-shared:
	@echo "The $@ target is no longer supported by this makefile."
	@false

test: pngtest$(EXEEXT)
	./pngtest$(EXEEXT)

pngtest$(EXEEXT): pngtest.o libpng.a
	$(LD) $(LDFLAGS) -o $@ pngtest.o libpng.a $(LIBS)

clean:
	$(RM_F) $(OBJS) libpng.a
	$(RM_F) $(LIBSO) $(LIBSOMAJ) pnglibconf.h
	$(RM_F) pngtest*.o pngtest$(EXEEXT) pngout.png

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
