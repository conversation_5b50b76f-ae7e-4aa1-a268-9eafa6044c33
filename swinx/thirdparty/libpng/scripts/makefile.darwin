# makefile for libpng on Darwin / macOS
# Copyright (C) 2014, 2018-2025 Cosmin Truta
# Copyright (C) 2002, 2004, 2006, 2008, 2010-2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 2001 <PERSON>
# derived from makefile.linux:
#  Copyright (C) 1998, 1999 <PERSON>
#  Copyright (C) 1996, 1997 <PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Library name:
LIBNAME=libpng16
PNGMAJ=16

# Shared library names:
LIBSO=$(LIBNAME).dylib
LIBSOMAJ=$(LIBNAME).$(PNGMAJ).dylib

# Utilities:
CC=cc
AR=ar
RANLIB=ranlib
LN_SF=ln -sf
CP=cp
RM_F=rm -f

# Compiler and linker flags
NOHWOPT = -DPNG_ARM_NEON_OPT=0 -DPNG_MIPS_MSA_OPT=0 \
          -DPNG_POWERPC_VSX_OPT=0 -DPNG_INTEL_SSE_OPT=0 \
          -DPNG_RISCV_RVV_OPT=0
STDC = -pedantic-errors
WARN = -Wall -Wextra -Wundef
WARNMORE = -Wcast-align -Wconversion -Wshadow -Wpointer-arith -Wwrite-strings \
           -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes
LOCAL_CPPFLAGS = $(NOHWOPT)
CPPFLAGS = # -DPNG_DEBUG=5
ALL_CPPFLAGS = $(LOCAL_CPPFLAGS) $(CPPFLAGS)
LOCAL_CFLAGS = $(STDC) $(WARN) # $(WARNMORE)
CFLAGS = -O3 -funroll-loops # -g
ALL_CFLAGS = $(LOCAL_CFLAGS) $(CFLAGS)
ARFLAGS = rc
LDFLAGS = -L. -lpng16 -lz # -g
LDFLAGS_A = libpng.a -lz -lm # -g

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

# File lists
OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

OBJSDLL = $(OBJS:.o=.pic.o)

.SUFFIXES:      .c .o .pic.o

.c.o:
	$(CC) -c $(ALL_CPPFLAGS) $(ALL_CFLAGS) -o $@ $*.c

.c.pic.o:
	$(CC) -c $(ALL_CPPFLAGS) $(ALL_CFLAGS) -fno-common -o $@ $*.c

all: libpng.a $(LIBSO) pngtest pngtest-static

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	$(CP) $(PNGLIBCONF_H_PREBUILT) $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)

$(LIBSOMAJ): $(OBJSDLL)
	$(CC) -dynamiclib \
	      -current_version 16 -compatibility_version 16 \
	      -o $(LIBSOMAJ) \
	      $(OBJSDLL) -lz

pngtest: pngtest.o $(LIBSO)
	$(CC) -o pngtest $(CFLAGS) pngtest.o $(LDFLAGS)

pngtest-static: pngtest.o libpng.a
	$(CC) -o pngtest-static $(CFLAGS) pngtest.o $(LDFLAGS_A)

test: pngtest pngtest-static
	@echo ""
	@echo "   Running pngtest dynamically linked with $(LIBSO):"
	@echo ""
	./pngtest
	@echo ""
	@echo "   Running pngtest statically linked with libpng.a:"
	@echo ""
	./pngtest-static

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-static:
	@echo "The $@ target is no longer supported by this makefile."
	@false

install-shared:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) $(OBJS) $(OBJSDLL) libpng.a
	$(RM_F) $(LIBNAME).*dylib pnglibconf.h
	$(RM_F) pngtest*.o pngtest pngtest-static pngout.png

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o      png.pic.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o pngerror.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o   pngget.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o   pngmem.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o pngpread.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o  pngread.pic.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o   pngrio.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o pngrtran.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o pngrutil.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o   pngset.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o pngtrans.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o   pngwio.pic.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o pngwrite.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o pngwtran.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o pngwutil.pic.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o: png.h pngconf.h pnglibconf.h
