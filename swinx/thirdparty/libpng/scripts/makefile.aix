# makefile for libpng using gcc (generic, static library)
# Copyright (C) 2000, 2020-2025 Cosmin Truta
# Copyright (C) 2002, 2006-2009, 2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 2000 <PERSON> (AIX support added, from makefile.gcc)
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Location of the zlib library and include files
ZLIBINC = ../zlib
ZLIBLIB = ../zlib

# Compiler, linker, lib and other tools
CC = gcc
LD = $(CC)
AR = ar
RANLIB = ranlib
RM_F = rm -f

LIBNAME = libpng16
PNGMAJ = 16

CPPFLAGS = -I$(ZLIBINC) # -DPNG_DEBUG=5
CFLAGS = -O2 # -g
ARFLAGS = rc
LDFLAGS = -L. -L$(ZLIBLIB) -lpng16 -lz -lm # -g

# File lists
OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

# Targets
.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

all: $(LIBNAME).a pngtest

include scripts/pnglibconf.mak
REMOVE = $(RM_F)
DFNFLAGS = $(DEFS) $(CPPFLAGS)

$(LIBNAME).a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

test: pngtest
	./pngtest

pngtest: pngtest.o $(LIBNAME).a
	$(LD) -o $@ pngtest.o $(LDFLAGS)

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	$(RM_F) *.o $(LIBNAME).a pngtest pngout.png pnglibconf.h

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
