# makefile for libpng
# Copyright (C) 2018-2025 Co<PERSON>in <PERSON>
# Copyright (C) 1998-2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

CC=cc
AR=ar
#RANLIB=ranlib
RANLIB=echo
CPPFLAGS=-I../zlib -DSYSV -Dmips
CFLAGS=-O -systype sysv -w
#CFLAGS=-O
ARFLAGS=rc
LDFLAGS=-L. -L../zlib/ -lpng -lz -lm

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = png.o pngerror.o pngget.o pngmem.o pngpread.o \
       pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
       pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $*.c

all: libpng.a pngtest

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	cp $(PNGLIBCONF_H_PREBUILT) $@

libpng.a: $(OBJS)
	$(AR) $(ARFLAGS) $@ $(OBJS)
	$(RANLIB) $@

pngtest: pngtest.o libpng.a
	$(CC) -o pngtest $(CFLAGS) pngtest.o $(LDFLAGS)

test: pngtest
	./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

clean:
	rm -f *.o libpng.a pngtest pngout.png pnglibconf.h

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
