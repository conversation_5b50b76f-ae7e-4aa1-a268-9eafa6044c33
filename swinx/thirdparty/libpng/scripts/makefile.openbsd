# makefile for libpng
# Copyright (C) 2018-2025 Cosmin <PERSON>
# Copyright (C) 2007-2009, 2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

SHLIB_MAJOR=	16
SHLIB_MINOR=	0

LIB=	png
SRCS=	png.c pngerror.c pngget.c pngmem.c pngpread.c \
	pngread.c pngrio.c pngrtran.c pngrutil.c pngset.c \
	pngtrans.c pngwio.c pngwrite.c pngwtran.c pngwutil.c
HDRS=	png.h pngconf.h pnglibconf.h

CPPFLAGS+=	-I${.CURDIR}
CFLAGS+=	-Wall -Wextra -Wundef

CLEANFILES+=	pngtest.o pngtest pnglibconf.h

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT= scripts/pnglibconf.h.prebuilt

.c.o:
	${CC} -c ${CPPFLAGS} ${CFLAGS} -o $@ $*.c

pnglibconf.h:	${PNGLIBCONF_H_PREBUILT}
	cp ${PNGLIBCONF_H_PREBUILT} $@

pngtest.o:	pngtest.c
	${CC} -c ${CPPFLAGS} ${CFLAGS} ${.ALLSRC} -o ${.TARGET}

pngtest:	pngtest.o
	${CC} ${LDFLAGS} ${.ALLSRC} -o ${.TARGET} -L${.OBJDIR} -lpng -lz -lm

test:	pngtest
	env LD_LIBRARY_PATH="${.OBJDIR}" ./pngtest

install:
	@echo "The $@ target is no longer supported by this makefile."
	@false

.include <bsd.lib.mk>
