include (sources.cmake)

set(supported_cairo_headers ${cairo_headers})
set(unsupported_cairo_headers)
set(all_cairo_headers ${cairo_headers})
set(all_cairo_private ${cairo_private})
set(all_cairo_cxx_sources ${cairo_cxx_sources})
set(all_cairo_sources ${cairo_sources})

set(enabled_cairo_headers ${cairo_headers})
set(enabled_cairo_private ${cairo_private})
set(enabled_cairo_cxx_sources ${cairo_cxx_sources})
set(enabled_cairo_sources ${cairo_sources})

set(all_cairo_pkgconf cairo.pc)
set(enabled_cairo_pkgconf cairo.pc)

set(supported_cairo_headers ${cairo_xlib_headers})
list(APPEND all_cairo_headers ${cairo_xlib_headers})
list(APPEND all_cairo_private ${cairo_xlib_private})
list(APPEND all_cairo_cxx_sources ${cairo_xlib_cxx_sources})
list(APPEND all_cairo_sources ${cairo_xlib_sources})
if(CAIRO_HAS_XLIB_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_xlib_headers})
list(APPEND enabled_cairo_private ${cairo_xlib_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_xlib_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_xlib_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-xlib.pc)
if(CAIRO_HAS_XLIB_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-xlib.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_xlib_xrender_headers})
list(APPEND all_cairo_headers ${cairo_xlib_xrender_headers})
list(APPEND all_cairo_private ${cairo_xlib_xrender_private})
list(APPEND all_cairo_cxx_sources ${cairo_xlib_xrender_cxx_sources})
list(APPEND all_cairo_sources ${cairo_xlib_xrender_sources})
if(CAIRO_HAS_XLIB_XRENDER_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_xlib_xrender_headers})
list(APPEND enabled_cairo_private ${cairo_xlib_xrender_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_xlib_xrender_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_xlib_xrender_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-xlib-xrender.pc)
if(CAIRO_HAS_XLIB_XRENDER_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-xlib-xrender.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_xcb_headers})
list(APPEND all_cairo_headers ${cairo_xcb_headers})
list(APPEND all_cairo_private ${cairo_xcb_private})
list(APPEND all_cairo_cxx_sources ${cairo_xcb_cxx_sources})
list(APPEND all_cairo_sources ${cairo_xcb_sources})
if(CAIRO_HAS_XCB_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_xcb_headers})
list(APPEND enabled_cairo_private ${cairo_xcb_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_xcb_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_xcb_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-xcb.pc)
if(CAIRO_HAS_XCB_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-xcb.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_xlib_xcb_headers})
list(APPEND all_cairo_headers ${cairo_xlib_xcb_headers})
list(APPEND all_cairo_private ${cairo_xlib_xcb_private})
list(APPEND all_cairo_cxx_sources ${cairo_xlib_xcb_cxx_sources})
list(APPEND all_cairo_sources ${cairo_xlib_xcb_sources})
if(CAIRO_HAS_XLIB_XCB_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_xlib_xcb_headers})
list(APPEND enabled_cairo_private ${cairo_xlib_xcb_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_xlib_xcb_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_xlib_xcb_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-xlib-xcb.pc)
if(CAIRO_HAS_XLIB_XCB_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-xlib-xcb.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_xcb_shm_headers})
list(APPEND all_cairo_headers ${cairo_xcb_shm_headers})
list(APPEND all_cairo_private ${cairo_xcb_shm_private})
list(APPEND all_cairo_cxx_sources ${cairo_xcb_shm_cxx_sources})
list(APPEND all_cairo_sources ${cairo_xcb_shm_sources})
if(CAIRO_HAS_XCB_SHM_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_xcb_shm_headers})
list(APPEND enabled_cairo_private ${cairo_xcb_shm_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_xcb_shm_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_xcb_shm_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-xcb-shm.pc)
if(CAIRO_HAS_XCB_SHM_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-xcb-shm.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_qt_headers})
list(APPEND all_cairo_headers ${cairo_qt_headers})
list(APPEND all_cairo_private ${cairo_qt_private})
list(APPEND all_cairo_cxx_sources ${cairo_qt_cxx_sources})
list(APPEND all_cairo_sources ${cairo_qt_sources})
if (CAIRO_HAS_QT_SURFACE})
list(APPEND enabled_cairo_headers ${cairo_qt_headers})
list(APPEND enabled_cairo_private ${cairo_qt_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_qt_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_qt_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-qt.pc)
if(CAIRO_HAS_QT_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-qt.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_quartz_headers})
list(APPEND all_cairo_headers ${cairo_quartz_headers})
list(APPEND all_cairo_private ${cairo_quartz_private})
list(APPEND all_cairo_cxx_sources ${cairo_quartz_cxx_sources})
list(APPEND all_cairo_sources ${cairo_quartz_sources})
if(CAIRO_HAS_QUARTZ_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_quartz_headers})
list(APPEND enabled_cairo_private ${cairo_quartz_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_quartz_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_quartz_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-quartz.pc)
if(CAIRO_HAS_QUARTZ_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-quartz.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_quartz_font_headers})
list(APPEND all_cairo_headers ${cairo_quartz_font_headers})
list(APPEND all_cairo_private ${cairo_quartz_font_private})
list(APPEND all_cairo_cxx_sources ${cairo_quartz_font_cxx_sources})
list(APPEND all_cairo_sources ${cairo_quartz_font_sources})
if(CAIRO_HAS_QUARTZ_FONT)
list(APPEND enabled_cairo_headers ${cairo_quartz_font_headers})
list(APPEND enabled_cairo_private ${cairo_quartz_font_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_quartz_font_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_quartz_font_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-quartz-font.pc)
if(CAIRO_HAS_QUARTZ_FONT)
list(APPEND enabled_cairo_pkgconf cairo-quartz-font.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_quartz_image_headers})
list(APPEND all_cairo_headers ${cairo_quartz_image_headers})
list(APPEND all_cairo_private ${cairo_quartz_image_private})
list(APPEND all_cairo_cxx_sources ${cairo_quartz_image_cxx_sources})
list(APPEND all_cairo_sources ${cairo_quartz_image_sources})
if(CAIRO_HAS_QUARTZ_IMAGE_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_quartz_image_headers})
list(APPEND enabled_cairo_private ${cairo_quartz_image_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_quartz_image_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_quartz_image_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-quartz-image.pc)
if(CAIRO_HAS_QUARTZ_IMAGE_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-quartz-image.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_win32_headers})
list(APPEND all_cairo_headers ${cairo_win32_headers})
list(APPEND all_cairo_private ${cairo_win32_private})
list(APPEND all_cairo_cxx_sources ${cairo_win32_cxx_sources})
list(APPEND all_cairo_sources ${cairo_win32_sources})
if(CAIRO_HAS_WIN32_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_win32_headers})
list(APPEND enabled_cairo_private ${cairo_win32_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_win32_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_win32_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-win32.pc)
if(CAIRO_HAS_WIN32_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-win32.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_win32_font_headers})
list(APPEND all_cairo_headers ${cairo_win32_font_headers})
list(APPEND all_cairo_private ${cairo_win32_font_private})
list(APPEND all_cairo_cxx_sources ${cairo_win32_font_cxx_sources})
list(APPEND all_cairo_sources ${cairo_win32_font_sources})
if(CAIRO_HAS_WIN32_FONT)
list(APPEND enabled_cairo_headers ${cairo_win32_font_headers})
list(APPEND enabled_cairo_private ${cairo_win32_font_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_win32_font_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_win32_font_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-win32-font.pc)
if(CAIRO_HAS_WIN32_FONT)
list(APPEND enabled_cairo_pkgconf cairo-win32-font.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_skia_headers})
list(APPEND all_cairo_headers ${cairo_skia_headers})
list(APPEND all_cairo_private ${cairo_skia_private})
list(APPEND all_cairo_cxx_sources ${cairo_skia_cxx_sources})
list(APPEND all_cairo_sources ${cairo_skia_sources})
if(CAIRO_HAS_SKIA_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_skia_headers})
list(APPEND enabled_cairo_private ${cairo_skia_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_skia_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_skia_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-skia.pc)
if(CAIRO_HAS_SKIA_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-skia.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_os2_headers})
list(APPEND all_cairo_headers ${cairo_os2_headers})
list(APPEND all_cairo_private ${cairo_os2_private})
list(APPEND all_cairo_cxx_sources ${cairo_os2_cxx_sources})
list(APPEND all_cairo_sources ${cairo_os2_sources})
if(CAIRO_HAS_OS2_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_os2_headers})
list(APPEND enabled_cairo_private ${cairo_os2_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_os2_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_os2_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-os2.pc)
if(CAIRO_HAS_OS2_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-os2.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_beos_headers})
list(APPEND all_cairo_headers ${cairo_beos_headers})
list(APPEND all_cairo_private ${cairo_beos_private})
list(APPEND all_cairo_cxx_sources ${cairo_beos_cxx_sources})
list(APPEND all_cairo_sources ${cairo_beos_sources})
if(CAIRO_HAS_BEOS_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_beos_headers})
list(APPEND enabled_cairo_private ${cairo_beos_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_beos_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_beos_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-beos.pc)
if(CAIRO_HAS_BEOS_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-beos.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_drm_headers})
list(APPEND all_cairo_headers ${cairo_drm_headers})
list(APPEND all_cairo_private ${cairo_drm_private})
list(APPEND all_cairo_cxx_sources ${cairo_drm_cxx_sources})
list(APPEND all_cairo_sources ${cairo_drm_sources})
if(CAIRO_HAS_DRM_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_drm_headers})
list(APPEND enabled_cairo_private ${cairo_drm_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_drm_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_drm_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-drm.pc)
if(CAIRO_HAS_DRM_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-drm.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_gallium_headers})
list(APPEND all_cairo_headers ${cairo_gallium_headers})
list(APPEND all_cairo_private ${cairo_gallium_private})
list(APPEND all_cairo_cxx_sources ${cairo_gallium_cxx_sources})
list(APPEND all_cairo_sources ${cairo_gallium_sources})
if(CAIRO_HAS_GALLIUM_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_gallium_headers})
list(APPEND enabled_cairo_private ${cairo_gallium_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_gallium_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_gallium_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-gallium.pc)
if(CAIRO_HAS_GALLIUM_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-gallium.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_png_headers})
list(APPEND all_cairo_headers ${cairo_png_headers})
list(APPEND all_cairo_private ${cairo_png_private})
list(APPEND all_cairo_cxx_sources ${cairo_png_cxx_sources})
list(APPEND all_cairo_sources ${cairo_png_sources})
if(CAIRO_HAS_PNG_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_png_headers})
list(APPEND enabled_cairo_private ${cairo_png_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_png_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_png_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-png.pc)
if(CAIRO_HAS_PNG_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-png.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_gl_headers})
list(APPEND all_cairo_headers ${cairo_gl_headers})
list(APPEND all_cairo_private ${cairo_gl_private})
list(APPEND all_cairo_cxx_sources ${cairo_gl_cxx_sources})
list(APPEND all_cairo_sources ${cairo_gl_sources})
if(CAIRO_HAS_GL_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_gl_headers})
list(APPEND enabled_cairo_private ${cairo_gl_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_gl_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_gl_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-gl.pc)
if(CAIRO_HAS_GL_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-gl.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_glesv2_headers})
list(APPEND all_cairo_headers ${cairo_glesv2_headers})
list(APPEND all_cairo_private ${cairo_glesv2_private})
list(APPEND all_cairo_cxx_sources ${cairo_glesv2_cxx_sources})
list(APPEND all_cairo_sources ${cairo_glesv2_sources})
if(CAIRO_HAS_GLESV2_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_glesv2_headers})
list(APPEND enabled_cairo_private ${cairo_glesv2_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_glesv2_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_glesv2_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-glesv2.pc)
if(CAIRO_HAS_GLESV2_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-glesv2.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_directfb_headers})
list(APPEND all_cairo_headers ${cairo_directfb_headers})
list(APPEND all_cairo_private ${cairo_directfb_private})
list(APPEND all_cairo_cxx_sources ${cairo_directfb_cxx_sources})
list(APPEND all_cairo_sources ${cairo_directfb_sources})
if(CAIRO_HAS_DIRECTFB_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_directfb_headers})
list(APPEND enabled_cairo_private ${cairo_directfb_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_directfb_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_directfb_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-directfb.pc)
if(CAIRO_HAS_DIRECTFB_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-directfb.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_vg_headers})
list(APPEND all_cairo_headers ${cairo_vg_headers})
list(APPEND all_cairo_private ${cairo_vg_private})
list(APPEND all_cairo_cxx_sources ${cairo_vg_cxx_sources})
list(APPEND all_cairo_sources ${cairo_vg_sources})
if(CAIRO_HAS_VG_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_vg_headers})
list(APPEND enabled_cairo_private ${cairo_vg_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_vg_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_vg_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-vg.pc)
if(CAIRO_HAS_VG_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-vg.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_egl_headers})
list(APPEND all_cairo_headers ${cairo_egl_headers})
list(APPEND all_cairo_private ${cairo_egl_private})
list(APPEND all_cairo_cxx_sources ${cairo_egl_cxx_sources})
list(APPEND all_cairo_sources ${cairo_egl_sources})
if(CAIRO_HAS_EGL_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_egl_headers})
list(APPEND enabled_cairo_private ${cairo_egl_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_egl_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_egl_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-egl.pc)
if(CAIRO_HAS_EGL_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-egl.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_glx_headers})
list(APPEND all_cairo_headers ${cairo_glx_headers})
list(APPEND all_cairo_private ${cairo_glx_private})
list(APPEND all_cairo_cxx_sources ${cairo_glx_cxx_sources})
list(APPEND all_cairo_sources ${cairo_glx_sources})
if(CAIRO_HAS_GLX_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_glx_headers})
list(APPEND enabled_cairo_private ${cairo_glx_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_glx_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_glx_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-glx.pc)
if(CAIRO_HAS_GLX_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-glx.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_wgl_headers})
list(APPEND all_cairo_headers ${cairo_wgl_headers})
list(APPEND all_cairo_private ${cairo_wgl_private})
list(APPEND all_cairo_cxx_sources ${cairo_wgl_cxx_sources})
list(APPEND all_cairo_sources ${cairo_wgl_sources})
if(CAIRO_HAS_WGL_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_wgl_headers})
list(APPEND enabled_cairo_private ${cairo_wgl_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_wgl_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_wgl_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-wgl.pc)
if(CAIRO_HAS_WGL_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-wgl.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_script_headers})
list(APPEND all_cairo_headers ${cairo_script_headers})
list(APPEND all_cairo_private ${cairo_script_private})
list(APPEND all_cairo_cxx_sources ${cairo_script_cxx_sources})
list(APPEND all_cairo_sources ${cairo_script_sources})
if(CAIRO_HAS_SCRIPT_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_script_headers})
list(APPEND enabled_cairo_private ${cairo_script_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_script_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_script_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-script.pc)
if(CAIRO_HAS_SCRIPT_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-script.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_ft_headers})
list(APPEND all_cairo_headers ${cairo_ft_headers})
list(APPEND all_cairo_private ${cairo_ft_private})
list(APPEND all_cairo_cxx_sources ${cairo_ft_cxx_sources})
list(APPEND all_cairo_sources ${cairo_ft_sources})
if(CAIRO_HAS_FT_FONT)
list(APPEND enabled_cairo_headers ${cairo_ft_headers})
list(APPEND enabled_cairo_private ${cairo_ft_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_ft_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_ft_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-ft.pc)
if(CAIRO_HAS_FT_FONT)
list(APPEND enabled_cairo_pkgconf cairo-ft.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_fc_headers})
list(APPEND all_cairo_headers ${cairo_fc_headers})
list(APPEND all_cairo_private ${cairo_fc_private})
list(APPEND all_cairo_cxx_sources ${cairo_fc_cxx_sources})
list(APPEND all_cairo_sources ${cairo_fc_sources})
if(CAIRO_HAS_FC_FONT)
list(APPEND enabled_cairo_headers ${cairo_fc_headers})
list(APPEND enabled_cairo_private ${cairo_fc_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_fc_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_fc_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-fc.pc)
if(CAIRO_HAS_FC_FONT)
list(APPEND enabled_cairo_pkgconf cairo-fc.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_ps_headers})
list(APPEND all_cairo_headers ${cairo_ps_headers})
list(APPEND all_cairo_private ${cairo_ps_private})
list(APPEND all_cairo_cxx_sources ${cairo_ps_cxx_sources})
list(APPEND all_cairo_sources ${cairo_ps_sources})
if(CAIRO_HAS_PS_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_ps_headers})
list(APPEND enabled_cairo_private ${cairo_ps_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_ps_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_ps_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-ps.pc)
if(CAIRO_HAS_PS_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-ps.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_pdf_headers})
list(APPEND all_cairo_headers ${cairo_pdf_headers})
list(APPEND all_cairo_private ${cairo_pdf_private})
list(APPEND all_cairo_cxx_sources ${cairo_pdf_cxx_sources})
list(APPEND all_cairo_sources ${cairo_pdf_sources})
if(CAIRO_HAS_PDF_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_pdf_headers})
list(APPEND enabled_cairo_private ${cairo_pdf_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_pdf_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_pdf_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-pdf.pc)
if(CAIRO_HAS_PDF_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-pdf.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_svg_headers})
list(APPEND all_cairo_headers ${cairo_svg_headers})
list(APPEND all_cairo_private ${cairo_svg_private})
list(APPEND all_cairo_cxx_sources ${cairo_svg_cxx_sources})
list(APPEND all_cairo_sources ${cairo_svg_sources})
if(CAIRO_HAS_SVG_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_svg_headers})
list(APPEND enabled_cairo_private ${cairo_svg_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_svg_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_svg_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-svg.pc)
if(CAIRO_HAS_SVG_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-svg.pc)
endif()

list(APPEND all_cairo_private ${cairo_test_surfaces_private} ${cairo_test_surfaces_headers})
list(APPEND all_cairo_cxx_sources ${cairo_test_surfaces_cxx_sources})
list(APPEND all_cairo_sources ${cairo_test_surfaces_sources})
if(CAIRO_HAS_TEST_SURFACES)
list(APPEND enabled_cairo_private ${cairo_test_surfaces_private} ${cairo_test_surfaces_headers})
list(APPEND enabled_cairo_cxx_sources ${cairo_test_surfaces_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_test_surfaces_sources})
endif()

list(APPEND supported_cairo_headers ${cairo_image_headers})
list(APPEND all_cairo_headers ${cairo_image_headers})
list(APPEND all_cairo_private ${cairo_image_private})
list(APPEND all_cairo_cxx_sources ${cairo_image_cxx_sources})
list(APPEND all_cairo_sources ${cairo_image_sources})
list(APPEND enabled_cairo_headers ${cairo_image_headers})
list(APPEND enabled_cairo_private ${cairo_image_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_image_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_image_sources})

list(APPEND supported_cairo_headers ${cairo_mime_headers})
list(APPEND all_cairo_headers ${cairo_mime_headers})
list(APPEND all_cairo_private ${cairo_mime_private})
list(APPEND all_cairo_cxx_sources ${cairo_mime_cxx_sources})
list(APPEND all_cairo_sources ${cairo_mime_sources})
list(APPEND enabled_cairo_headers ${cairo_mime_headers})
list(APPEND enabled_cairo_private ${cairo_mime_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_mime_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_mime_sources})

list(APPEND supported_cairo_headers ${cairo_recording_headers})
list(APPEND all_cairo_headers ${cairo_recording_headers})
list(APPEND all_cairo_private ${cairo_recording_private})
list(APPEND all_cairo_cxx_sources ${cairo_recording_cxx_sources})
list(APPEND all_cairo_sources ${cairo_recording_sources})
list(APPEND enabled_cairo_headers ${cairo_recording_headers})
list(APPEND enabled_cairo_private ${cairo_recording_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_recording_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_recording_sources})

list(APPEND supported_cairo_headers ${cairo_observer_headers})
list(APPEND all_cairo_headers ${cairo_observer_headers})
list(APPEND all_cairo_private ${cairo_observer_private})
list(APPEND all_cairo_cxx_sources ${cairo_observer_cxx_sources})
list(APPEND all_cairo_sources ${cairo_observer_sources})
list(APPEND enabled_cairo_headers ${cairo_observer_headers})
list(APPEND enabled_cairo_private ${cairo_observer_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_observer_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_observer_sources})

list(APPEND unsupported_cairo_headers ${cairo_tee_headers})
list(APPEND all_cairo_headers ${cairo_tee_headers})
list(APPEND all_cairo_private ${cairo_tee_private})
list(APPEND all_cairo_cxx_sources ${cairo_tee_cxx_sources})
list(APPEND all_cairo_sources ${cairo_tee_sources})
if(CAIRO_HAS_TEE_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_tee_headers})
list(APPEND enabled_cairo_private ${cairo_tee_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_tee_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_tee_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-tee.pc)
if(CAIRO_HAS_TEE_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-tee.pc)
endif()

list(APPEND unsupported_cairo_headers ${cairo_xml_headers})
list(APPEND all_cairo_headers ${cairo_xml_headers})
list(APPEND all_cairo_private ${cairo_xml_private})
list(APPEND all_cairo_cxx_sources ${cairo_xml_cxx_sources})
list(APPEND all_cairo_sources ${cairo_xml_sources})
if(CAIRO_HAS_XML_SURFACE)
list(APPEND enabled_cairo_headers ${cairo_xml_headers})
list(APPEND enabled_cairo_private ${cairo_xml_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_xml_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_xml_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-xml.pc)
if(CAIRO_HAS_XML_SURFACE)
list(APPEND enabled_cairo_pkgconf cairo-xml.pc)
endif()

list(APPEND supported_cairo_headers ${cairo_user_headers})
list(APPEND all_cairo_headers ${cairo_user_headers})
list(APPEND all_cairo_private ${cairo_user_private})
list(APPEND all_cairo_cxx_sources ${cairo_user_cxx_sources})
list(APPEND all_cairo_sources ${cairo_user_sources})
list(APPEND enabled_cairo_headers ${cairo_user_headers})
list(APPEND enabled_cairo_private ${cairo_user_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_user_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_user_sources})

list(APPEND all_cairo_private ${cairo_pthread_private} ${cairo_pthread_headers})
list(APPEND all_cairo_cxx_sources ${cairo_pthread_cxx_sources})
list(APPEND all_cairo_sources ${cairo_pthread_sources})
if(CAIRO_HAS_PTHREAD)
list(APPEND enabled_cairo_private ${cairo_pthread_private} ${cairo_pthread_headers})
list(APPEND enabled_cairo_cxx_sources ${cairo_pthread_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_pthread_sources})
endif()

list(APPEND supported_cairo_headers ${cairo_gobject_headers})
list(APPEND all_cairo_headers ${cairo_gobject_headers})
list(APPEND all_cairo_private ${cairo_gobject_private})
list(APPEND all_cairo_cxx_sources ${cairo_gobject_cxx_sources})
list(APPEND all_cairo_sources ${cairo_gobject_sources})
if(CAIRO_HAS_GOBJECT_FUNCTIONS)
list(APPEND enabled_cairo_headers ${cairo_gobject_headers})
list(APPEND enabled_cairo_private ${cairo_gobject_private})
list(APPEND enabled_cairo_cxx_sources ${cairo_gobject_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_gobject_sources})
endif()
list(APPEND all_cairo_pkgconf cairo-gobject.pc)
if(CAIRO_HAS_GOBJECT_FUNCTIONS)
list(APPEND enabled_cairo_pkgconf cairo-gobject.pc)
endif()

list(APPEND all_cairo_private ${cairo_trace_private} ${cairo_trace_headers})
list(APPEND all_cairo_cxx_sources ${cairo_trace_cxx_sources})
list(APPEND all_cairo_sources ${cairo_trace_sources})
if(CAIRO_HAS_TRACE)
list(APPEND enabled_cairo_private ${cairo_trace_private} ${cairo_trace_headers})
list(APPEND enabled_cairo_cxx_sources ${cairo_trace_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_trace_sources})
endif()

list(APPEND all_cairo_private ${cairo_interpreter_private} ${cairo_interpreter_headers})
list(APPEND all_cairo_cxx_sources ${cairo_interpreter_cxx_sources})
list(APPEND all_cairo_sources ${cairo_interpreter_sources})
if(CAIRO_HAS_INTERPRETER)
list(APPEND enabled_cairo_private ${cairo_interpreter_private} ${cairo_interpreter_headers})
list(APPEND enabled_cairo_cxx_sources ${cairo_interpreter_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_interpreter_sources})
endif()

list(APPEND all_cairo_private ${cairo_symbol_lookup_private} ${cairo_symbol_lookup_headers})
list(APPEND all_cairo_cxx_sources ${cairo_symbol_lookup_cxx_sources})
list(APPEND all_cairo_sources ${cairo_symbol_lookup_sources})
if(CAIRO_HAS_SYMBOL_LOOKUP)
list(APPEND enabled_cairo_private ${cairo_symbol_lookup_private} ${cairo_symbol_lookup_headers})
list(APPEND enabled_cairo_cxx_sources ${cairo_symbol_lookup_cxx_sources})
list(APPEND enabled_cairo_sources ${cairo_symbol_lookup_sources})
endif()

set(SOURCES ${enabled_cairo_sources})

add_library(cairo SHARED ${SOURCES})
target_link_libraries(cairo ${CAIRO_LIBS})

# Add dependencies to ensure proper build order
add_dependencies(cairo pixman-1 freetype fontconfig)

add_library(cairo-static ${SOURCES} ${STATIC_SOURCES})
target_link_libraries(cairo-static ${CAIRO_LIBS})

# Add dependencies to ensure proper build order
add_dependencies(cairo-static pixman-1 freetype fontconfig)

install(TARGETS cairo RUNTIME DESTINATION bin ARCHIVE DESTINATION lib LIBRARY DESTINATION lib)
install(TARGETS cairo-static RUNTIME DESTINATION bin ARCHIVE DESTINATION lib LIBRARY DESTINATION lib)

install(FILES ${enabled_cairo_headers} DESTINATION include/cairo)
install(FILES ../cairo-version.h DESTINATION include/cairo)
install(FILES ${CMAKE_BINARY_DIR}/cairo-features.h DESTINATION include/cairo)
