Possible upgrades to gzfilebuf:

- The ability to do putback (e.g. putbackfail)

- The ability to seek (zlib supports this, but could be slow/tricky)

- Simultaneous read/write access (does it make sense?)

- Support for ios_base::ate open mode

- Locale support?

- Check public interface to see which calls give problems
  (due to dependence on library internals)

- Override operator<<(ostream&, gzfilebuf*) to allow direct copying
  of stream buffer to stream ( i.e. os << is.rdbuf(); )
