@PACKAGE_INIT@

set(_minizip_supported_components "")
include(CMakeFindDependencyMacro)

if(@MINIZIP_BUILD_SHARED@)
    list(APPEND _minizip_supported_components shared)
endif(@MINIZIP_BUILD_SHARED@)

if(@MINIZIP_BUILD_STATIC@)
    list(APPEND _minizip_supported_components static)
endif(@MINIZIP_BUILD_STATIC@)

if(@MINIZIP_ENABLE_BZIP2@)
    find_dependency(BZip2)
endif(@MINIZIP_ENABLE_BZIP2@)

if(minizip_FIND_COMPONENTS)
    find_dependency(ZLIB CONFIG COMPONENTS ${minizip_FIND_COMPONENTS})

    foreach(_comp ${minizip_FIND_COMPONENTS})
        if(NOT _comp IN_LIST _MINIZIP_supported_components)
            set(minizip_FOUND False)
            set(minizip_NOT_FOUND_MESSAGE "Unsupported component: ${_comp}")
        endif(NOT _comp IN_LIST _MINIZIP_supported_components)

        include("${CMAKE_CURRENT_LIST_DIR}/minizip-${_comp}.cmake")
    endforeach(_comp ${ZLIB_FIND_COMPONENTS})
else(minizip_FIND_COMPONENTS)
    find_dependency(ZLIB CONFIG)

    foreach(_component_config IN LISTS _minizip_supported_components)
        include("${CMAKE_CURRENT_LIST_DIR}/minizip-${_component_config}.cmake")
    endforeach(_component_config IN LISTS _minizip_supported_components)
endif(minizip_FIND_COMPONENTS)
