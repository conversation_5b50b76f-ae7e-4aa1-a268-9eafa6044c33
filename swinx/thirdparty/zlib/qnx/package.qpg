<QPG:Generation>
   <QPG:Options>
      <QPG:User unattended="no" verbosity="2" listfiles="yes"/>
      <QPG:Defaults type="qnx_package"/>
      <QPG:Source></QPG:Source>
      <QPG:Release number="+"/>
      <QPG:Build></QPG:Build>
      <QPG:FileSorting strip="yes"/>
      <QPG:Package targets="combine"/>
      <QPG:Repository generate="yes"/>
      <QPG:FinalDir></QPG:FinalDir>
      <QPG:Cleanup></QPG:Cleanup>
   </QPG:Options>

   <QPG:Responsible>
      <QPG:Company></QPG:Company>
      <QPG:Department></QPG:Department>
      <QPG:Group></QPG:Group>
      <QPG:Team></QPG:Team>
      <QPG:Employee></QPG:Employee>
      <QPG:EmailAddress></QPG:EmailAddress>
   </QPG:Responsible>

   <QPG:Values>
      <QPG:Files>
         <QPG:Add file="../zconf.h" install="/opt/include/" user="root:sys" permission="644"/>
         <QPG:Add file="../zlib.h" install="/opt/include/" user="root:sys" permission="644"/>
         <QPG:Add file="../libz.so.*******" install="/opt/lib/" user="root:bin" permission="644"/>
         <QPG:Add file="libz.so" install="/opt/lib/" component="dev" filetype="symlink" linkto="libz.so.*******"/>
         <QPG:Add file="libz.so.1" install="/opt/lib/" filetype="symlink" linkto="libz.so.*******"/>
         <QPG:Add file="../libz.so.*******" install="/opt/lib/" component="slib"/>
      </QPG:Files>

      <QPG:PackageFilter>
         <QPM:PackageManifest>
            <QPM:PackageDescription>
               <QPM:PackageType>Library</QPM:PackageType>
               <QPM:PackageReleaseNotes></QPM:PackageReleaseNotes>
               <QPM:PackageReleaseUrgency>Medium</QPM:PackageReleaseUrgency>
               <QPM:PackageRepository></QPM:PackageRepository>
               <QPM:FileVersion>2.0</QPM:FileVersion>
            </QPM:PackageDescription>

            <QPM:ProductDescription>
               <QPM:ProductName>zlib</QPM:ProductName>
               <QPM:ProductIdentifier>zlib</QPM:ProductIdentifier>
               <QPM:ProductEmail><EMAIL></QPM:ProductEmail>
               <QPM:VendorName>Public</QPM:VendorName>
               <QPM:VendorInstallName>public</QPM:VendorInstallName>
               <QPM:VendorURL>www.gzip.org/zlib</QPM:VendorURL>
               <QPM:VendorEmbedURL></QPM:VendorEmbedURL>
               <QPM:VendorEmail></QPM:VendorEmail>
               <QPM:AuthorName>Jean-Loup Gailly,Mark Adler</QPM:AuthorName>
               <QPM:AuthorURL>www.gzip.org/zlib</QPM:AuthorURL>
               <QPM:AuthorEmbedURL></QPM:AuthorEmbedURL>
               <QPM:AuthorEmail><EMAIL></QPM:AuthorEmail>
               <QPM:ProductIconSmall></QPM:ProductIconSmall>
               <QPM:ProductIconLarge></QPM:ProductIconLarge>
               <QPM:ProductDescriptionShort>A massively spiffy yet delicately unobtrusive compression library.</QPM:ProductDescriptionShort>
               <QPM:ProductDescriptionLong>zlib is designed to be a free, general-purpose, legally unencumbered, lossless data compression library for use on virtually any computer hardware and operating system.</QPM:ProductDescriptionLong>
               <QPM:ProductDescriptionURL>https://zlib.net/</QPM:ProductDescriptionURL>
               <QPM:ProductDescriptionEmbedURL></QPM:ProductDescriptionEmbedURL>
            </QPM:ProductDescription>

            <QPM:ReleaseDescription>
               <QPM:ReleaseVersion>*******</QPM:ReleaseVersion>
               <QPM:ReleaseUrgency>Medium</QPM:ReleaseUrgency>
               <QPM:ReleaseStability>Stable</QPM:ReleaseStability>
               <QPM:ReleaseNoteMinor></QPM:ReleaseNoteMinor>
               <QPM:ReleaseNoteMajor></QPM:ReleaseNoteMajor>
               <QPM:ExcludeCountries>
                  <QPM:Country></QPM:Country>
               </QPM:ExcludeCountries>

               <QPM:ReleaseCopyright>No License</QPM:ReleaseCopyright>
            </QPM:ReleaseDescription>

            <QPM:ContentDescription>
               <QPM:ContentTopic xmlmultiple="true">Software Development/Libraries and Extensions/C Libraries</QPM:ContentTopic>
               <QPM:ContentKeyword>zlib,compression</QPM:ContentKeyword>
               <QPM:TargetOS>qnx6</QPM:TargetOS>
               <QPM:HostOS>qnx6</QPM:HostOS>
               <QPM:DisplayEnvironment xmlmultiple="true">None</QPM:DisplayEnvironment>
               <QPM:TargetAudience xmlmultiple="true">Developer</QPM:TargetAudience>
            </QPM:ContentDescription>
         </QPM:PackageManifest>
      </QPG:PackageFilter>

      <QPG:PackageFilter proc="none" target="none">
         <QPM:PackageManifest>
            <QPM:ProductInstallationDependencies>
               <QPM:ProductRequirements></QPM:ProductRequirements>
            </QPM:ProductInstallationDependencies>

            <QPM:ProductInstallationProcedure>
               <QPM:Script xmlmultiple="true">
                  <QPM:ScriptName></QPM:ScriptName>
                  <QPM:ScriptType>Install</QPM:ScriptType>
                  <QPM:ScriptTiming>Post</QPM:ScriptTiming>
                  <QPM:ScriptBlocking>No</QPM:ScriptBlocking>
                  <QPM:ScriptResult>Ignore</QPM:ScriptResult>
                  <QPM:ShortDescription></QPM:ShortDescription>
                  <QPM:UseBinaries>No</QPM:UseBinaries>
                  <QPM:Priority>Optional</QPM:Priority>
               </QPM:Script>
            </QPM:ProductInstallationProcedure>
         </QPM:PackageManifest>

         <QPM:Launch>
         </QPM:Launch>
      </QPG:PackageFilter>

      <QPG:PackageFilter type="core" component="none">
         <QPM:PackageManifest>
            <QPM:ProductInstallationProcedure>
	       <QPM:OrderDependency xmlmultiple="true">
	          <QPM:Order>InstallOver</QPM:Order>
	          <QPM:Product>zlib</QPM:Product>
	       </QPM:OrderDependency>
            </QPM:ProductInstallationProcedure>
         </QPM:PackageManifest>

         <QPM:Launch>
         </QPM:Launch>
      </QPG:PackageFilter>

      <QPG:PackageFilter type="core" component="dev">
         <QPM:PackageManifest>
            <QPM:ProductInstallationProcedure>
	       <QPM:OrderDependency xmlmultiple="true">
	          <QPM:Order>InstallOver</QPM:Order>
	          <QPM:Product>zlib-dev</QPM:Product>
	       </QPM:OrderDependency>
            </QPM:ProductInstallationProcedure>
         </QPM:PackageManifest>

         <QPM:Launch>
         </QPM:Launch>
      </QPG:PackageFilter>
   </QPG:Values>
</QPG:Generation>
