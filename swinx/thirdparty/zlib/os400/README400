        ZLIB version ******* for OS/400 installation instructions

1) Download and unpack the zlib tarball to some IFS directory.
   (i.e.: /path/to/the/zlib/ifs/source/directory)

   If the installed IFS command supports gzip format, this is straightforward,
else you have to unpack first to some directory on a system supporting it,
then move the whole directory to the IFS via the network (via SMB or FTP).

2) Edit the configuration parameters in the compilation script.

        EDTF STMF('/path/to/the/zlib/ifs/source/directory/os400/make.sh')

Tune the parameters according to your needs if not matching the defaults.
Save the file and exit after edition.

3) Enter qshell, then work in the zlib OS/400 specific directory.

        QSH
        cd /path/to/the/zlib/ifs/source/directory/os400

4) Compile and install

        sh make.sh

The script will:
- create the libraries, objects and IFS directories for the zlib environment,
- compile all modules,
- create a service program,
- create a static and a dynamic binding directory,
- install header files for C/C++ and for ILE/RPG, both for compilation in
  DB2 and IFS environments.

That's all. 


Notes:  For OS/400 ILE RPG programmers, a /copy member defining the ZLIB
                API prototypes for ILE RPG can be found in ZLIB/H(ZLIB.INC).
                In the ILE environment, the same definitions are available from
                file zlib.inc located in the same IFS include directory as the
                C/C++ header files.
                Please read comments in this member for more information.

        Remember that most foreign textual data are ASCII coded: this
                implementation does not handle conversion from/to ASCII, so
                text data code conversions must be done explicitly.

        Mainly for the reason above, always open zipped files in binary mode.
